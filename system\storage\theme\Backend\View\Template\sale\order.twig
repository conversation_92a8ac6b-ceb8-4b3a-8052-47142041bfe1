<!-- Order Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Поръчки</h1>
                <p class="text-gray-500 mt-1">Управление на всички поръчки в магазина</p>
            </div>
            {% if add_new_url %}
            <a href="{{ add_new_url }}" class="mt-4 md:mt-0 px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-add-line"></i>
                </div>
                <span>Нова поръчка</span>
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex flex-wrap items-center gap-4">
            <button id="filter-btn" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-filter-3-line"></i>
                </div>
                <span>Филтър</span>
            </button>
            <div class="w-full md:w-auto">
                <div class="relative">
                    <button id="status-dropdown-btn" class="w-full md:w-48 flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
                        <span>
                            {% set selected_status_text = 'Всички статуси' %}
                            {% if filter_order_status_id and status_options %}
                                {% for status in status_options %}
                                    {% if status.value == filter_order_status_id %}
                                        {% set selected_status_text = status.text %}
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                            {{ selected_status_text }}
                        </span>
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-arrow-down-s-line"></i>
                        </div>
                    </button>
                    <div id="status-dropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded shadow-lg">
                        <ul class="py-1">
                            {% if status_options %}
                                {% for status in status_options %}
                                <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" data-value="{{ status.value }}">{{ status.text }}</li>
                                {% endfor %}
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="w-full md:w-auto">
                <div class="relative">
                    <button id="period-dropdown-btn" class="w-full md:w-48 flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
                        <span>Период</span>
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-arrow-down-s-line"></i>
                        </div>
                    </button>
                    <div id="period-dropdown" class="hidden absolute z-10 w-64 mt-1 bg-white border border-gray-200 rounded shadow-lg p-4">
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">От дата</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">До дата</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                            </div>
                            <div class="flex justify-end">
                                <button class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Приложи</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full md:w-auto">
                <div class="relative">
                    <button id="sort-dropdown-btn" class="w-full md:w-48 flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
                        <span>Сортиране</span>
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-arrow-down-s-line"></i>
                        </div>
                    </button>
                    <div id="sort-dropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded shadow-lg">
                        <ul class="py-1">
                            {% if sort_options %}
                                {% for sort_option in sort_options %}
                                <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" data-value="{{ sort_option.value }}">{{ sort_option.text }}</li>
                                {% endfor %}
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Filter Modal -->
            <div id="filter-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
                <div class="bg-white rounded-lg w-full max-w-md mx-4">
                    <div class="flex justify-between items-center p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">Филтър</h3>
                        <button id="close-filter" class="text-gray-400 hover:text-gray-500">
                            <div class="w-6 h-6 flex items-center justify-center">
                                <i class="ri-close-line"></i>
                            </div>
                        </button>
                    </div>
                    <div class="p-6">
                        <form id="filter-form" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
                                    <option value="">Всички</option>
                                    <option value="new">Нова</option>
                                    <option value="processing">В процес</option>
                                    <option value="completed">Изпълнена</option>
                                    <option value="cancelled">Отказана</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Клиент</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Име или имейл на клиент">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Начин на плащане</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
                                    <option value="">Всички</option>
                                    <option value="card">Карта</option>
                                    <option value="bank">Банков превод</option>
                                    <option value="cash">Наложен платеж</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Сума</label>
                                <div class="flex space-x-2">
                                    <input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="От">
                                    <input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="До">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Период</label>
                                <div class="flex space-x-2">
                                    <input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                                    <input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                                </div>
                            </div>
                            <div class="flex justify-end space-x-2 mt-6">
                                <button type="button" id="reset-filter" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap">Изчисти</button>
                                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Приложи филтър</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <main class="flex-1 overflow-y-auto bg-gray-50 p-6">
        <!-- Orders Table -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    Номер
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Дата
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Клиент
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Начин на плащане
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Статус
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Сума
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% if orders %}
                            {% for order in orders %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <input type="checkbox" name="selected[]" value="{{ order.order_id }}" class="mr-2">
                                        <span class="text-sm font-medium text-gray-900">#{{ order.order_id }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ order.date_added }}</div>
                                    {% if order.date_modified %}
                                    <div class="text-sm text-gray-500">Изм: {{ order.date_modified }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">{{ order.customer }}</div>
                                    {% if order.email %}
                                    <div class="text-sm text-gray-500">{{ order.email }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center text-sm text-gray-900">
                                        <div class="w-5 h-5 flex items-center justify-center mr-2">
                                            {% if order.payment_method %}
                                                {% set payment_lower = order.payment_method|lower %}
                                                {% if 'card' in payment_lower %}
                                                    <i class="ri-bank-card-line"></i>
                                                {% elseif 'cash' in payment_lower or 'cod' in payment_lower %}
                                                    <i class="ri-cash-line"></i>
                                                {% elseif 'bank' in payment_lower %}
                                                    <i class="ri-bank-line"></i>
                                                {% else %}
                                                    <i class="ri-money-dollar-circle-line"></i>
                                                {% endif %}
                                            {% else %}
                                                <i class="ri-money-dollar-circle-line"></i>
                                            {% endif %}
                                        </div>
                                        {{ order.payment_method|default('Неизвестен') }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="status-badge {% if order.order_status_id == 1 %} status-new{% elseif order.order_status_id == 2 %} status-processing{% elseif order.order_status_id == 5 %} status-completed{% elseif order.order_status_id == 7 %} status-cancelled{% else %} status-unknown{% endif %}">{{ order.order_status }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ order.total }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-2">
                                        {% if order.info %}
                                        <a href="{{ order.info }}" class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full" title="Преглед">
                                            <div class="w-5 h-5 flex items-center justify-center">
                                                <i class="ri-eye-line"></i>
                                            </div>
                                        </a>
                                        {% endif %}
                                        {% if order.edit %}
                                        <a href="{{ order.edit }}" class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full" title="Редактиране">
                                            <div class="w-5 h-5 flex items-center justify-center">
                                                <i class="ri-edit-line"></i>
                                            </div>
                                        </a>
                                        {% endif %}
                                        {% if order.invoice %}
                                        <a href="{{ order.invoice }}" class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full" title="Фактура">
                                            <div class="w-5 h-5 flex items-center justify-center">
                                                <i class="ri-file-text-line"></i>
                                            </div>
                                        </a>
                                        {% endif %}
                                        <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full" title="Още опции">
                                            <div class="w-5 h-5 flex items-center justify-center">
                                                <i class="ri-more-2-line"></i>
                                            </div>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                    Няма намерени поръчки
                                </td>
                            </tr>
                        {% endif %}


                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if pagination_html %}
            {{ pagination_html|raw }}
            {% endif %}
        </div>
    </main>

<!-- Order Detail Modal -->
<div id="order-detail-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">Детайли на поръчка #ORD-2025-1001</h3>
            <button id="close-order-detail" class="text-gray-400 hover:text-gray-500">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-close-line"></i>
                </div>
            </button>
        </div>
        <div class="p-6">
            <div class="flex flex-col md:flex-row justify-between mb-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 mb-1">Информация за поръчката</h4>
                    <p class="text-sm"><span class="font-medium">Дата:</span> 23.04.2025 14:32</p>
                    <p class="text-sm"><span class="font-medium">Статус:</span> <span class="text-blue-600">Нова</span></p>
                    <p class="text-sm"><span class="font-medium">Плащане:</span> Карта</p>
                    <p class="text-sm"><span class="font-medium">Обща сума:</span> 1,599.00 лв.</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <h4 class="text-sm font-medium text-gray-500 mb-1">Информация за клиента</h4>
                    <p class="text-sm"><span class="font-medium">Име:</span> Георги Иванов</p>
                    <p class="text-sm"><span class="font-medium">Имейл:</span> <EMAIL></p>
                    <p class="text-sm"><span class="font-medium">Телефон:</span> +359 888 123 456</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <h4 class="text-sm font-medium text-gray-500 mb-1">Адрес за доставка</h4>
                    <p class="text-sm">ул. Иван Вазов 25, ап. 7</p>
                    <p class="text-sm">София, 1000</p>
                    <p class="text-sm">България</p>
                </div>
            </div>
            
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h4 class="font-medium text-gray-800 mb-4">Продукти в поръчката</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Продукт
                                </th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Количество
                                </th>
                                <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Ед. цена
                                </th>
                                <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Сума
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded">
                                            <img class="h-10 w-10 rounded object-cover object-top" src="https://readdy.ai/api/search-image?query=professional%2520photo%2520of%2520modern%2520smartphone%2520with%2520sleek%2520design%2520on%2520clean%2520white%2520background%252C%2520high%2520resolution%2520product%2520photography%252C%2520minimalist%2520style&width=100&height=100&seq=1&orientation=squarish" alt="">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">Смартфон Samsung Galaxy S22</div>
                                            <div class="text-sm text-gray-500">Код: PRD-1001</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 text-center text-sm text-gray-900">
                                    1
                                </td>
                                <td class="px-4 py-4 text-right text-sm text-gray-900">
                                    1,599.00 лв.
                                </td>
                                <td class="px-4 py-4 text-right text-sm font-medium text-gray-900">
                                    1,599.00 лв.
                                </td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr class="bg-gray-50">
                                <td colspan="2" class="px-4 py-3"></td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-700">Междинна сума:</td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-900">1,599.00 лв.</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td colspan="2" class="px-4 py-3"></td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-700">Доставка:</td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-900">0.00 лв.</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td colspan="2" class="px-4 py-3"></td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-700">Обща сума:</td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-primary">1,599.00 лв.</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h4 class="font-medium text-gray-800 mb-4">Промяна на статус</h4>
                <div class="flex flex-wrap items-center gap-4">
                    <button class="px-4 py-2 bg-blue-100 text-blue-700 rounded-button hover:bg-blue-200 text-sm whitespace-nowrap">Нова</button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 text-sm whitespace-nowrap">В процес</button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 text-sm whitespace-nowrap">Изпълнена</button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 text-sm whitespace-nowrap">Отказана</button>
                </div>
            </div>
            
            <div class="border-t border-gray-200 pt-6">
                <h4 class="font-medium text-gray-800 mb-4">Изпращане на известие</h4>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Тема</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" value="Информация за поръчка #ORD-2025-1001">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Съобщение</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm h-32" placeholder="Въведете текст на съобщението...">Здравейте, Георги Иванов!

Благодарим ви за вашата поръчка #ORD-2025-1001. 

С уважение,
Екипът на Rakla</textarea>
                </div>
                <div class="flex justify-end">
                    <button class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Изпрати известие</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter modal functionality
    const filterBtn = document.getElementById('filter-btn');
    const filterModal = document.getElementById('filter-modal');
    const closeFilter = document.getElementById('close-filter');
    const filterForm = document.getElementById('filter-form');
    const resetFilter = document.getElementById('reset-filter');
    
    filterBtn.addEventListener('click', function() {
        filterModal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    });
    
    closeFilter.addEventListener('click', function() {
        filterModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    });
    
    filterModal.addEventListener('click', function(e) {
        if (e.target === filterModal) {
            filterModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    });
    
    filterForm.addEventListener('submit', function(e) {
        e.preventDefault();
        filterModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    });
    
    resetFilter.addEventListener('click', function() {
        filterForm.reset();
    });
    
    // Toggle sidebar
    const toggleSidebar = document.getElementById('toggle-sidebar');
    const sidebar = document.getElementById('sidebar');
    const mobileMenu = document.getElementById('mobile-menu');
    
    toggleSidebar.addEventListener('click', function() {
        sidebar.classList.toggle('w-64');
        sidebar.classList.toggle('w-20');
        const sidebarItems = document.querySelectorAll('.sidebar-item span');
        const sidebarLogo = document.getElementById('sidebar-logo');
        sidebarItems.forEach(item => {
            item.classList.toggle('hidden');
        });
        sidebarLogo.classList.toggle('hidden');
        const icon = toggleSidebar.querySelector('i');
        if (icon.classList.contains('ri-menu-fold-line')) {
            icon.classList.remove('ri-menu-fold-line');
            icon.classList.add('ri-menu-unfold-line');
        } else {
            icon.classList.remove('ri-menu-unfold-line');
            icon.classList.add('ri-menu-fold-line');
        }
    });
    
    mobileMenu.addEventListener('click', function() {
        sidebar.classList.toggle('-translate-x-full');
    });
    
    // Status dropdown
    const statusDropdownBtn = document.getElementById('status-dropdown-btn');
    const statusDropdown = document.getElementById('status-dropdown');
    
    statusDropdownBtn.addEventListener('click', function() {
        statusDropdown.classList.toggle('hidden');
    });
    
    // Period dropdown
    const periodDropdownBtn = document.getElementById('period-dropdown-btn');
    const periodDropdown = document.getElementById('period-dropdown');
    
    periodDropdownBtn.addEventListener('click', function() {
        periodDropdown.classList.toggle('hidden');
    });
    
    // Sort dropdown
    const sortDropdownBtn = document.getElementById('sort-dropdown-btn');
    const sortDropdown = document.getElementById('sort-dropdown');
    
    sortDropdownBtn.addEventListener('click', function() {
        sortDropdown.classList.toggle('hidden');
    });
    
    // Per page dropdown
    const perPageDropdownBtn = document.getElementById('per-page-dropdown-btn');
    const perPageDropdown = document.getElementById('per-page-dropdown');
    
    perPageDropdownBtn.addEventListener('click', function() {
        perPageDropdown.classList.toggle('hidden');
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!statusDropdownBtn.contains(event.target) && !statusDropdown.contains(event.target)) {
            statusDropdown.classList.add('hidden');
        }
        if (!periodDropdownBtn.contains(event.target) && !periodDropdown.contains(event.target)) {
            periodDropdown.classList.add('hidden');
        }
        if (!sortDropdownBtn.contains(event.target) && !sortDropdown.contains(event.target)) {
            sortDropdown.classList.add('hidden');
        }
        if (!perPageDropdownBtn.contains(event.target) && !perPageDropdown.contains(event.target)) {
            perPageDropdown.classList.add('hidden');
        }
    });
    
    // Select status option
    const statusItems = statusDropdown.querySelectorAll('li');
    statusItems.forEach(item => {
        item.addEventListener('click', function() {
            statusDropdownBtn.querySelector('span').textContent = this.textContent;
            statusDropdown.classList.add('hidden');
        });
    });
    
    // Select sort option
    const sortItems = sortDropdown.querySelectorAll('li');
    sortItems.forEach(item => {
        item.addEventListener('click', function() {
            sortDropdownBtn.querySelector('span').textContent = this.textContent;
            sortDropdown.classList.add('hidden');
        });
    });
    
    // Select per page option
    const perPageItems = perPageDropdown.querySelectorAll('li');
    perPageItems.forEach(item => {
        item.addEventListener('click', function() {
            perPageDropdownBtn.querySelector('span').textContent = this.textContent;
            perPageDropdown.classList.add('hidden');
        });
    });
    
    // Order detail modal
    const viewOrderBtns = document.querySelectorAll('.ri-eye-line');
    const orderDetailModal = document.getElementById('order-detail-modal');
    const closeOrderDetail = document.getElementById('close-order-detail');
    
    viewOrderBtns.forEach(btn => {
        btn.parentElement.parentElement.addEventListener('click', function() {
            orderDetailModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        });
    });
    
    closeOrderDetail.addEventListener('click', function() {
        orderDetailModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    });
    
    orderDetailModal.addEventListener('click', function(e) {
        if (e.target === orderDetailModal) {
            orderDetailModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    });
});
</script>