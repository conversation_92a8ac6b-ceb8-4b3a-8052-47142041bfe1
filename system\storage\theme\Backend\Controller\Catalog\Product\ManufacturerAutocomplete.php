<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class ManufacturerAutocomplete extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }
    
    /**
     * Обработва заявките за автодопълване на производители
     * 
     * @param array $get GET параметри от заявката
     * @return array
     */
    public function autocomplete($get) {
        $json = [];
        
        
            $this->loadModelsAs([
                'catalog/manufacturer' => 'manufacturerModel'
            ]);
            
            $filter_data = [
                'start'       => 0,
                'limit'       => 10
            ];

            if (isset($get['filter_name'])) {
                $filter_data['filter_name'] = $get['filter_name'];
            }
            
            $results = $this->manufacturerModel->getManufacturers($filter_data);
            
            foreach ($results as $result) {
                $json[] = [
                    'id'   => $result['manufacturer_id'],
                    'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
                ];
            }
        
        
        return $json;
    }
}
