Създавам модифицирана версия на Opencart, като пренасочвам action-ите, моделите и изгледите към нови контролери, модели и изгледи, които съдържат модифициран код и се намират в директорията - system/storage/theme. Пренасочването се осъществява само за такива, които са добавени в модифицирната структура. В главната папка с име "theme" на модифицирната версия на Opencart съм разделил системата на Backend (съответства на admin) и Frontend (съответства на catalog) и добавил новите контролери, модели и изгледи. Разликата е, че в новата структура на папките името на контролера, модела и изгледа са с главна буква, а в оригиналната е с малка. В главната папка съм поставил някои важни файлове, които са необходими за работата на модифицираната версия на темата. Моля, запознай се с файловата структура на папка F:\Web\Rakla.bg - NEW\system\storage\theme.
Обръщам внимание към някои важни неща:
- във файла F:\Web\Rakla.bg - NEW\system\storage\theme\Controller.php се съдържа модифицирана версия на класа Controller, която разширява оригиналния клас Controller, която добавя нови методи, в които се извежда често употребяван код в Opencart, като по този начин се избягва повторение на кода и кода се структурира по-добре. Например метода getAdminLink() генериа линк към admin контролера и добавя автоматично user_token към заявките. Има и подобен метод, с който се генерират по няколко admin линка наведнъж. Това е само пример за един от новите методи, които са добавени в новия клас Controller. Разгледай и останалите методи, които могат да заменят много код в контролерите.
- във файла F:\Web\Rakla.bg - NEW\system\storage\theme\Data.php се съдържа клас, който съдържа методи, които връщат различен вид данни. Моля, запознай се с методите.
- във файла F:\Web\Rakla.bg - NEW\system\storage\theme\EnvLoader.php се съдържа клас, който съдържа методи, които се занимават с зареждането на .env файла и връщат стойности от него. Моля, запознай се с методите и с .env файла F:\Web\Rakla.bg - NEW\system\storage\theme\.env.
- във файла F:\Web\Rakla.bg - NEW\system\storage\theme\SecondDB.php се съдържа клас, който разширява оригиналния клас DB и добавя идентификатор за втората база данни. Той се използва, за да се извлича информация от друга база данни, когато е необходимо. Предимно се използва за връзка със старата база данни, докато се разработва новата версия на сайта. От старата база данни се използват таблици с информация за продуктите, потребителите, поръчките и др.
- във файла F:\Web\Rakla.bg - NEW\system\storage\theme\ThemeStyles.php се съдържа клас, който съдържа константи за CSS селектори, които се използват в темата. Моля, запознай се с константите и с тяхното използване.
- във файла F:\Web\Rakla.bg - NEW\system\storage\theme\ControllerSubMethods.php се съдържа клас, който се използва за извикване на суб-контролери и методи в тях. Това цели да се разделя кодът на по-малки части и да се постигне по-добра структура. Моля, запознай се с методите и с тяхното използване.
- във файла F:\Web\Rakla.bg - NEW\system\storage\theme\ConfigManager.php се съдържа клас, който се занимава със зареждането и превключването на конфигурацията за различните бази данни. Той се използва, за да се извлече информация от конфигурационните файлове и да се превключва между конфигурациите за двете бази данни.
- шаблоните се състоят само от основното тяло на съдържанието на страницата без хедъра и футъра. Те се съдържат в папките Backend/View/Template и Frontend/View/Template. Хедъра и футъра са в отделни шаблони. Когато се рендира страница, се рендират съответно хедърът, тялото и футърът като се ползва шаблонът common/layout.