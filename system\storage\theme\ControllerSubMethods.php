<?php

namespace Theme25;

class ControllerSubMethods {

    protected $_controller;

    public function __construct($controller = '') {
        $this->_controller = $controller;
    }

    
    public function __call($name, $arguments) {
        if (method_exists($this->_controller, $name)) {
            return call_user_func_array([$this->_controller, $name], $arguments);
        }
        trigger_error('Call to undefined method ' . get_class($this->_controller) . '::' . $name . '()', E_USER_ERROR);
    }

    public function __get($name) {
        return $this->_controller->$name;
    }

    public function __set($name, $value) {
        $this->_controller->$name = $value;
    }

    public function __isset($name) {
        return isset($this->_controller->$name);
    }
}