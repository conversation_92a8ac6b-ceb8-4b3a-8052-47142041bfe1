<?php

namespace Theme25\Backend\Model\Tool;

class ImageService extends \Model {
    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Вътрешен метод за обработка и преоразмеряване на изображение, с fallback към placeholder.
     *
     * @param string $image_filename_relative Относителен път до основното изображение.
     * @param int $width Желана ширина.
     * @param int $height Желана височина.
     * @param array &$result Референция към масива с резултати, който ще бъде модифициран.
     */
    private function _processAndResizeImage(string $image_filename_relative, int $width, int $height, array &$result): void {
        $this->load->model('tool/image'); // Зареждаме OpenCart модела за изображения

        $primary_attempted = false;
        $current_error_message = $result['error'] ?? null; // Запазваме начална грешка, ако има
        $result['error'] = null; // Нулираме грешката за обработката на изображението

        // Първи опит: с подадения $image_filename_relative
        if (!empty($image_filename_relative)) {
            $primary_attempted = true;
            if (is_file($this->getImagePath(true) . $image_filename_relative)) {
                $result['original_image_server_path'] = $this->getImagePath(true) . $image_filename_relative;
                $resized_url = $this->model_tool_image->resize($image_filename_relative, $width, $height);

                if ($resized_url) {
                    $result['success'] = true;
                    $result['resized_image_url'] = $resized_url;
                    $result['error'] = $current_error_message; // Възстановяваме начална грешка, ако е имало и сме успели
                    return; // Успех с основното изображение
                } else {
                    $result['error'] = 'Failed to resize primary image: ' . htmlspecialchars($image_filename_relative);
                }
            } else {
                $result['error'] = 'Primary image not found: ' . htmlspecialchars($image_filename_relative);
            }
        } else {
            // $image_filename_relative е празен, не се счита за грешка тук, директно се преминава към placeholder.
        }

        // Втори опит: с placeholder изображение
        $placeholder_filename = ThemeData()->getNoImageFile(false);
        if (!empty($placeholder_filename) && is_file(DIR_IMAGE . $placeholder_filename)) {
            $result['original_image_server_path'] = DIR_IMAGE . $placeholder_filename; // ETag ще е за placeholder
            $resized_placeholder_url = $this->model_tool_image->resize($placeholder_filename, $width, $height);

            if ($resized_placeholder_url) {
                $result['resized_image_url'] = $resized_placeholder_url;
                $result['success'] = true; // Успех, ако placeholder е зареден

                $placeholder_message = 'Using placeholder.';
                if ($primary_attempted && isset($result['error'])) { // Ако основното е пробвано и е имало грешка
                    $result['error'] .= ' | ' . $placeholder_message;
                } elseif (!$primary_attempted && empty($result['error'])) { // Ако не е подадено основно изображение
                    $result['error'] = 'No specific image requested. ' . $placeholder_message;
                }
                // Ако $current_error_message съществува, трябва да го комбинираме
                if ($current_error_message) {
                    $result['error'] = $current_error_message . ($result['error'] ? ' | ' . $result['error'] : '');
                }

            } else {
                $placeholder_error = 'Failed to resize placeholder: ' . htmlspecialchars($placeholder_filename);
                $result['error'] = isset($result['error']) ? $result['error'] . ' | ' . $placeholder_error : $placeholder_error;
                $result['resized_image_url'] = '';
                $result['success'] = false;
            }
        } else {
            $placeholder_not_found_error = 'Placeholder image not configured or not found.';
            $result['error'] = isset($result['error']) ? $result['error'] . ' | ' . $placeholder_not_found_error : $placeholder_not_found_error;
            $result['resized_image_url'] = '';
            $result['success'] = false;
        }
        
        // Комбиниране на $current_error_message с текущата грешка, ако $current_error_message съществува и не е вече включен
        if ($current_error_message && (empty($result['error']) || strpos($result['error'], $current_error_message) === false)) {
             $result['error'] = $current_error_message . ($result['error'] ? ' | ' . $result['error'] : '');
        }

        // Ако всичко е неуспешно, original_image_server_path не трябва да сочи към несъществуващ файл
        if (!$result['success'] && (empty($result['original_image_server_path']) || !is_file($result['original_image_server_path']))) {
            $result['original_image_server_path'] = '';
        }
    }

    /**
     * Извлича и преоразмерява изображение за продукт или placeholder.
     */
    public function getProductImageDetails(int $product_id, int $width, int $height): array {
        $this->load->model('catalog/product');

        $result = [
            'success' => false,
            'resized_image_url' => '',
            'original_image_server_path' => '',
            'error' => null
        ];

        try {
            $product_info = $this->model_catalog_product->getProduct($product_id);

            if (!$product_info) {
                // Задаваме грешката в $result, за да може _processAndResizeImage да я запази/комбинира
                $result['error'] = 'Продуктът не е намерен'; 
                // Дори продуктът да не е намерен, може да искаме да върнем placeholder.
                // Подаваме празен път, за да се опита placeholder.
                $this->_processAndResizeImage('', $width, $height, $result);
            } else {
                $original_image_filename = $product_info['image'];
                $this->_processAndResizeImage($original_image_filename, $width, $height, $result);
            }

        } catch (\Exception $e) {
            // Този catch е за неочаквани грешки, не за 'Продуктът не е намерен', тъй като това се обработва по-горе.
            $result['error'] = ($result['error'] ? $result['error'] . ' | ' : '') . 'Unexpected error: ' . $e->getMessage();
            $result['success'] = false;
            if (empty($result['resized_image_url'])) { // Последен опит за placeholder при тотална грешка
                 $this->_processAndResizeImage('', $width, $height, $result); // Ще опита placeholder
            }
        }
        return $result;
    }

    /**
     * Извлича и преоразмерява изображение по неговия относителен път или placeholder.
     */
    public function getImageDetailsByPath(string $relativePath, int $width, int $height): array {
        $result = [
            'success' => false,
            'resized_image_url' => '',
            'original_image_server_path' => '',
            'error' => null
        ];

        try {
            $this->_processAndResizeImage($relativePath, $width, $height, $result);
        } catch (\Exception $e) {
            $result['error'] = ($result['error'] ? $result['error'] . ' | ' : '') . 'Unexpected error: ' . $e->getMessage();
            $result['success'] = false;
            if (empty($result['resized_image_url'])) { // Последен опит за placeholder
                 $this->_processAndResizeImage('', $width, $height, $result); // Ще опита placeholder
            }
        }
        return $result;
    }

    private function getImagePath($theme_path = true) {
        return $theme_path ? ThemeData()->getImageServerPath() : DIR_IMAGE;
    }
}
