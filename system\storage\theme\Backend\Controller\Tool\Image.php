<?php

namespace Theme25\Backend\Controller\Tool;

class Image extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'tool/image');
    }

    /**
     * Обработва AJAX заявка за изображение
     * Връща изображение или placeholder ако изображението не може да бъде заредено
     */
    public function index() {
        // Проверка дали заявката е AJAX - остава същото
        $isAjax = !empty($this->request->server['HTTP_X_REQUESTED_WITH']) &&
                  strtolower($this->request->server['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        // Задаване на хедъри за JSON отговор
        $this->response->addHeader('Content-Type: application/json');
        $json = ['success' => false, 'image' => '', 'error' => ''];

        // Проверка за user_token - остава същото
        if (!isset($this->session->data['user_token']) ||
            !isset($this->request->get['user_token']) ||
            ($this->session->data['user_token'] != $this->request->get['user_token'])) {
            $json['error'] = 'Невалиден token за достъп';
            $this->response->setOutput(json_encode($json));
            return;
        }

        // Проверка за необходимите параметри - остава същото
        if (!isset($this->request->get['product_id']) || !isset($this->request->get['width']) || !isset($this->request->get['height'])) {
            $json['error'] = 'Липсват задължителни параметри (product_id, width, height)';
            $this->response->setOutput(json_encode($json));
            return;
        }

        // Извличане на параметрите - остава същото
        $product_id = (int)$this->request->get['product_id'];
        $width = (int)$this->request->get['width'];
        $height = (int)$this->request->get['height'];

        // Зареждане на новия модел за обслужване на изображения
        $this->load->model('tool/Imageservice'); // Ще стане $this->model_tool_ImageService

        // Извикване на метода от новия модел
        $image_details = $this->model_tool_Imageservice->getProductImageDetails($product_id, $width, $height);

        // ETag и Last-Modified логика, базирана на original_image_server_path от модела
        $etag = '';
        $last_modified = '';
        $original_file_for_etag = $image_details['original_image_server_path'];

        // Генериране на ETag и Last-Modified, ако имаме валиден оригинален файл
        if (!empty($original_file_for_etag) && is_file($original_file_for_etag)) {
            $file_time = filemtime($original_file_for_etag);
            if ($image_details['success']) {
                // ETag за успешно извлечено изображение
                $etag = md5($product_id . '_' . $width . '_' . $height . '_' . $file_time);
            } else {
                // ETag за placeholder или изображение при грешка
                $etag = md5('error_fallback_' . $product_id . '_' . $width . '_' . $height . '_' . $file_time);
            }
            $last_modified = gmdate('D, d M Y H:i:s', $file_time) . ' GMT';
        }

        // Проверка дали клиентът има кеширана версия
        $client_etag = isset($this->request->server['HTTP_IF_NONE_MATCH']) ? trim($this->request->server['HTTP_IF_NONE_MATCH']) : '';
        $client_last_modified = isset($this->request->server['HTTP_IF_MODIFIED_SINCE']) ? trim($this->request->server['HTTP_IF_MODIFIED_SINCE']) : '';

        // Ако клиентът има актуална версия (и имаме ETag/Last-Modified), връщаме 304 Not Modified
        if ($etag && $last_modified &&
            (($client_etag && $client_etag == $etag) || (!$client_etag && $client_last_modified && $client_last_modified == $last_modified))) {
            $this->response->addHeader('HTTP/1.1 304 Not Modified');
            $this->response->addHeader('ETag: ' . $etag);
            $this->response->addHeader('Last-Modified: ' . $last_modified);
            $this->response->addHeader('Cache-Control: public, max-age=86400'); // 24 часа
            $this->response->setOutput('');
            return;
        }

        // Обработка на резултата от модела
        if ($image_details['success']) {
            $json['success'] = true;
            $json['image'] = $image_details['resized_image_url'];
        } else {
            $json['success'] = false; // Изрично задаваме false, дори ако има placeholder
            $json['image'] = $image_details['resized_image_url']; // Може да е URL на placeholder или празен стринг
            $json['error'] = $image_details['error'] ? $image_details['error'] : 'Неуспешно зареждане на изображението.';
        }
        
        // Добавяне на хедъри за кеширане за 200 OK отговор
        // Тези хедъри се добавят независимо дали е успешно или грешка с placeholder,
        // стига да имаме ETag и Last-Modified (т.е. имаме някакъв файл, за който да ги генерираме)
        if ($etag) {
            $this->response->addHeader('ETag: ' . $etag);
        }
        if ($last_modified) {
            $this->response->addHeader('Last-Modified: ' . $last_modified);
        }
        // Cache-Control се задава винаги за 200 OK отговори, за да може клиентът да кешира резултата
        $this->response->addHeader('Cache-Control: public, max-age=86400'); // 24 часа

        // Връщане на JSON отговор
        $this->response->setOutput(json_encode($json));
    }
}
