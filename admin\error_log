[31-May-2025 03:49:10 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method avramov_funcs::log() in /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product/Edit.php:92
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product.php(37): Theme25\Backend\Controller\Catalog\Product\Edit->prepareProductForm()
#1 [internal function]: Theme25\Backend\Controller\Catalog\Product->edit()
#2 /home/<USER>/storage_theme25/theme/RequestProcessor.php(78): call_user_func_array(Array, Array)
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCatal...', 'edit', Array)
#4 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCatal...', 'edit', Array)
#5 /home/<USER>/storage_theme25/modification/system/engine/action.php(82): RequestProcessor->process(Object(ControllerCatalogProduct), 'edit', Array)
#6 /home/<USER>/storage_theme25/modification/system/engine/action.php(57): Action->callRequestProcessor(Objec in /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product/Edit.php on line 92
