[31-May-2025 04:52:53 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Theme25\Backend\Controller\Catalog\Product::get() in /home/<USER>/theme25/system/engine/controller.php:10
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Controller.php(86): Controller->__get('load')
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product/CategoryAutocomplete.php(21): Theme25\Controller->loadModelAs('catalog/categor...', 'categoryModel')
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product.php(57): Theme25\Backend\Controller\Catalog\Product\CategoryAutocomplete->autocomplete(Array)
#3 [internal function]: Theme25\Backend\Controller\Catalog\Product->autocomplete()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(78): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCatal...', 'autocomplete', Array)
#6 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('Contro in /home/<USER>/theme25/system/engine/controller.php on line 10
[31-May-2025 04:54:19 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Theme25\Backend\Controller\Catalog\Product::get() in /home/<USER>/theme25/system/engine/controller.php:10
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Controller.php(86): Controller->__get('load')
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product/CategoryAutocomplete.php(21): Theme25\Controller->loadModelAs('catalog/categor...', 'categoryModel')
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product.php(57): Theme25\Backend\Controller\Catalog\Product\CategoryAutocomplete->autocomplete(Array)
#3 [internal function]: Theme25\Backend\Controller\Catalog\Product->autocomplete()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(78): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCatal...', 'autocomplete', Array)
#6 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('Contro in /home/<USER>/theme25/system/engine/controller.php on line 10
[31-May-2025 04:59:56 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Theme25\Backend\Controller\Catalog\Product::get() in /home/<USER>/theme25/system/engine/controller.php:10
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Controller.php(87): Controller->__get('load')
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product/CategoryAutocomplete.php(21): Theme25\Controller->loadModelAs('catalog/categor...', 'categoryModel')
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product.php(57): Theme25\Backend\Controller\Catalog\Product\CategoryAutocomplete->autocomplete(Array)
#3 [internal function]: Theme25\Backend\Controller\Catalog\Product->autocomplete()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(78): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCatal...', 'autocomplete', Array)
#6 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('Contro in /home/<USER>/theme25/system/engine/controller.php on line 10
[31-May-2025 05:00:12 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Theme25\Backend\Controller\Catalog\Product::get() in /home/<USER>/theme25/system/engine/controller.php:10
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Controller.php(87): Controller->__get('load')
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product/CategoryAutocomplete.php(21): Theme25\Controller->loadModelAs('catalog/categor...', 'categoryModel')
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product.php(57): Theme25\Backend\Controller\Catalog\Product\CategoryAutocomplete->autocomplete(Array)
#3 [internal function]: Theme25\Backend\Controller\Catalog\Product->autocomplete()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(78): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCatal...', 'autocomplete', Array)
#6 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('Contro in /home/<USER>/theme25/system/engine/controller.php on line 10
