<?php

namespace Theme25\Backend\Controller\Catalog;

class Category extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'catalog/category');
    }

	public function index() {
		$this->setTitle('Продукти');

		// Инициализиране на данните
		$this->initAdminData();

		// Подготовка на данните с верижно извикване на методи
		// $this->prepareProductListData()
		// 	 ->prepareFilterOptions()
		// 	 ->prepareProductItems()
		// 	 ->preparePagination();

		// Рендиране на шаблона с данните от $this->data
		$this->renderTemplateWithDataAndOutput('catalog/category');
	}

	
}
