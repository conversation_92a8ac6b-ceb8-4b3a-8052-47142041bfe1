<!-- Product Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
<div class="flex flex-col md:flex-row md:items-center justify-between">
<div class="flex items-center">
<a href="{{ back_url }}" data-readdy="true" class="mr-3 text-gray-500 hover:text-primary">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-arrow-left-line ri-lg"></i>
</div>
</a>
<div>
<h1 class="text-2xl font-bold text-gray-800">{{ heading_title }}</h1>
</div>
</div>
<div class="flex items-center space-x-3 mt-4 md:mt-0">
<a href="{{ back }}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors whitespace-nowrap !rounded-button">
<span>Отказ</span>
</a>
<button type="submit" form="product-form" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-save-line"></i>
</div>
<span>Запази</span>
</button>
</div>
</div>
</div>
<!-- Autocomplete Styles -->
<style>
.autocomplete-suggestions {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    background-color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.autocomplete-suggestion {
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.autocomplete-suggestion:hover {
    background-color: #f3f4f6;
}

.autocomplete-suggestion.active {
    background-color: #e5e7eb;
}
</style>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
<form id="product-form" method="post" action="{{ catalog_controller|raw }}&action=save" class="space-y-6" enctype="multipart/form-data">
    <input type="hidden" name="product_id" value="{{ product_id ?? 0 }}">
<div class="max-w-7xl">
<div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
<div class="border-b border-gray-200">
<div class="flex overflow-x-auto">
<button type="button" data-tab="tab-basic-info" class="tab-button active px-6 py-4 text-sm font-medium whitespace-nowrap">Основна информация</button>
<button type="button" data-tab="tab-images" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Изображения</button>
<button type="button" data-tab="tab-description" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Описание</button>
<button type="button" data-tab="tab-attributes" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Характеристики</button>
<button type="button" data-tab="tab-seo" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">SEO</button>
</div>
</div>
<!-- Tab Content -->
<div class="p-6">
<!-- Basic Info Tab -->
<div id="tab-basic-info" class="tab-content">
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Име на продукта <span class="text-red-500">*</span></label>
<input type="text" name="product_description[{{ language.language_id }}][name]" value="{{ product_description[language.language_id].name }}" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете име на продукта">
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Код на продукта <span class="text-red-500">*</span></label>
<input type="text" name="model" value="{{ model }}" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете код на продукта">
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Категория <span class="text-red-500">*</span></label>
<div class="relative" id="category-autocomplete">
<input type="text" id="category-input" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете категория" autocomplete="off" value="{{ product_category_name }}">
<input type="hidden" id="category-id" name="category_id" value="{{ category_id }}">
<div id="category-suggestions" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg hidden"></div>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Марка</label>
<div class="relative" id="brand-autocomplete">
<input type="text" id="brand-input" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете марка" autocomplete="off" value="{{ manufacturer_name }}">
<input type="hidden" id="brand-id" name="manufacturer_id" value="{{ manufacturer_id }}">
<div id="brand-suggestions" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg hidden"></div>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Цена <span class="text-red-500">*</span></label>
<div class="relative rounded-md shadow-sm">
<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
<span class="text-gray-500 sm:text-sm">лв.</span>
</div>
<input type="text" name="price" value="{{ price }}" class="pl-10 block w-full border-gray-300 rounded focus:ring-primary focus:border-primary sm:text-sm" placeholder="0.00">
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Промоционална цена (лв.)</label>
<div class="relative">
<input type="number" step="0.01" name="special_price" value="{{ special_price ?? '' }}" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="0.00">
<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
<span class="text-gray-500 text-sm">лв.</span>
</div>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Количество <span class="text-red-500">*</span></label>
<input type="number" name="quantity" min="0" class="w-32 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" value="{{ quantity }}">
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
<div class="flex items-center space-x-2 mt-2">
<label class="toggle-switch">
<input type="checkbox" name="status" {{ status is defined and status ? 'checked' : '' }}>
<span class="toggle-slider"></span>
</label>
<span class="text-sm text-gray-700">Активен</span>
</div>
</div>
</div>
</div>
</div>

<!-- Images Tab -->
<div id="tab-images" class="tab-content hidden">
    <!-- Main Image Upload -->
    <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">Основно изображение</label>
        <div class="flex items-center space-x-4">
            <div class="relative">
                <img id="image-preview" src="{{ thumb is defined ? thumb : 'https://via.placeholder.com/150' }}" alt="Preview" class="h-20 w-20 object-cover rounded border border-gray-300">
                <input type="hidden" name="image" id="image" value="{{ image is defined ? image : '' }}">
            </div>
            <div>
                <button type="button" id="button-upload" class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 text-sm flex items-center">
                    <i class="ri-upload-line mr-1"></i> Качи изображение
                </button>
                <p class="mt-1 text-xs text-gray-500">Препоръчителен размер: 800x800px</p>
            </div>
        </div>
    </div>

    <!-- Additional Images -->
    <div class="mb-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-sm font-medium text-gray-700">Допълнителни изображения</h3>
            <button type="button" id="add-more-images" class="text-sm text-primary hover:text-primary/80 flex items-center">
                <i class="ri-add-line mr-1"></i> Добави още
            </button>
        </div>
        
        <!-- Image Drop Zone -->
        <div class="image-upload-area rounded-lg p-8 flex flex-col items-center justify-center cursor-pointer border-2 border-dashed border-gray-300 hover:border-primary transition-colors mb-6" id="drop-zone">
            <div class="w-16 h-16 flex items-center justify-center text-gray-400 mb-4">
                <i class="ri-image-add-line ri-2x"></i>
            </div>
            <p class="text-sm text-gray-500 mb-1">Плъзнете и пуснете изображения тук</p>
            <p class="text-xs text-gray-400">или</p>
            <button type="button" id="browse-files" class="mt-4 px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors cursor-pointer">
                Изберете файлове
            </button>
            <p class="text-xs text-gray-400 mt-4">Поддържани формати: JPG, PNG, GIF. Максимален размер: 5MB</p>
        </div>

        <!-- Gallery Grid -->
        <div id="additional-images-container" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {% if product_images is defined %}
                {% for image in product_images %}
                    <div class="relative group">
                        <div class="aspect-square rounded-lg overflow-hidden border border-gray-200">
                            <img src="{{ image.thumb }}" alt="" class="w-full h-full object-cover">
                            <input type="hidden" name="product_image[]" value="{{ image.image }}">
                            <input type="hidden" name="product_image_sort_order[]" value="{{ image.sort_order }}">
                        </div>
                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                            <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" data-action="edit">
                                <i class="ri-edit-line"></i>
                            </button>
                            <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-700" data-action="remove">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            <input type="number" name="product_image_sort_order[]" value="{{ image.sort_order }}" 
                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded" 
                                   placeholder="Подредба">
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
</div>
</div>

<!-- Description Tab -->
<div id="tab-description" class="tab-content hidden">
<div class="mt-6">
<div class="border border-gray-300 rounded overflow-hidden">
<div class="bg-gray-50 border-b border-gray-300 p-2 flex space-x-2">
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-bold"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-italic"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-underline"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-list-unordered"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-list-ordered"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-link"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-image-line"></i>
</div>
</button>
</div>
<textarea name="product_description[{{ language.language_id }}]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" rows="6" placeholder="Въведете описание на продукта">{{ product_description[language.language_id] }}</textarea>
</div>
</div>


</div>

<!-- Attributes Tab -->
<div id="tab-attributes" class="tab-content hidden">
    <div class="mb-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-sm font-medium text-gray-700">Характеристики</h3>
            <button type="button" id="add-attribute" class="px-3 py-1 bg-primary text-white text-sm rounded hover:bg-primary/90">
                <i class="ri-add-line mr-1"></i> Добави характеристика
            </button>
        </div>
        <div id="attributes-container">
            {% if product_attributes is defined %}
                {% for attribute in product_attributes %}
                    <div class="attribute-item grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg mb-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Име</label>
                            <input type="text" name="product_attribute[{{ attribute.attribute_id }}][name]" value="{{ attribute.name }}" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Цвят, Размер">
                        </div>
                        <div class="flex">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Стойност</label>
                                <input type="text" name="product_attribute[{{ attribute.attribute_id }}][value]" value="{{ attribute.value }}" class="w-full px-3 py-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Червен, XL">
                            </div>
                            <button type="button" class="remove-attribute px-3 py-2 bg-red-50 text-red-500 border border-gray-300 border-l-0 rounded-r hover:bg-red-100 transition-colors">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-delete-bin-line"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
</div>

<!-- SEO Tab -->
<div id="tab-seo" class="tab-content hidden">
    <div class="mb-6">
        <div class="grid grid-cols-1 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">SEO URL</label>
                <input type="text" name="keyword" value="{{ keyword }}" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете SEO URL">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Мета таг заглавие</label>
                <input type="text" name="product_description[{{ language.language_id }}][meta_title]" value="{{ product_description[language.language_id].meta_title ?? '' }}" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете мета заглавие">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Мета таг описание</label>
                <textarea name="product_description[{{ language.language_id }}][meta_description]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" rows="3" placeholder="Въведете мета описание">{{ product_description[language.language_id].meta_description ?? '' }}</textarea>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Мета таг ключови думи</label>
                <input type="text" name="product_description[{{ language.language_id }}][meta_keyword]" value="{{ product_description[language.language_id].meta_keyword ?? '' }}" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете ключови думи, разделени със запетая">
            </div>
        </div>
    </div>
</div>

<!-- Specifications Tab (Hidden by default) -->
<div id="tab-specifications" class="tab-content hidden">
<div class="mb-4 flex justify-between items-center">
<h3 class="text-sm font-medium text-gray-700">Характеристики на продукта</h3>
<button id="add-specification" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-add-line"></i>
</div>
<span>Добави</span>
</button>
</div>
<div id="specifications-container" class="space-y-4">
{% if product_specifications is defined %}
    {% for specification in product_specifications %}
        <div class="specification-item grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Име</label>
                <input type="text" name="product_specification[{{ specification.id }}][name]" value="{{ specification.name }}" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Цвят, Размер, Материал">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Стойност</label>
                <div class="flex">
                    <input type="text" name="product_specification[{{ specification.id }}][value]" value="{{ specification.value }}" class="flex-1 px-3 py-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Червен, XL, Памук">
                    <button type="button" class="remove-specification px-3 py-2 bg-red-50 text-red-500 border border-gray-300 border-l-0 rounded-r hover:bg-red-100 transition-colors">
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-delete-bin-line"></i>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    {% endfor %}
{% else %}
    <div class="specification-item grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg">
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Име</label>
            <input type="text" name="product_specification[0][name]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Цвят, Размер, Материал">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Стойност</label>
            <div class="flex">
                <input type="text" name="product_specification[0][value]" class="flex-1 px-3 py-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Червен, XL, Памук">
                <button type="button" class="remove-specification px-3 py-2 bg-red-50 text-red-500 border border-gray-300 border-l-0 rounded-r hover:bg-red-100 transition-colors">
                    <div class="w-5 h-5 flex items-center justify-center">
                        <i class="ri-delete-bin-line"></i>
                    </div>
                </button>
            </div>
        </div>
    </div>
{% endif %}
</div>
</div>
</div>
</form></main>