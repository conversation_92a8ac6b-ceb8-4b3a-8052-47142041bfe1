<?php

namespace Theme25\Backend\Controller\Sale;

class Order extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'sale/order');
    }

	public function index() {
		$this->setTitle('Поръчки');

		// Инициализиране на данните
		$this->initAdminData();

		try {
			$subController = $this->setBackendSubController('Sale/Order/Index', $this);

			// Подготовка на данните
			$subController->prepareData();

			// Добавяне на основни данни ако липсват
			if (!isset($this->data['orders'])) {
				$this->setData('orders', []);
			}
			if (!isset($this->data['status_options'])) {
				$this->setData('status_options', [['value' => '', 'text' => 'Всички статуси']]);
			}
			if (!isset($this->data['sort_options'])) {
				$this->setData('sort_options', [
					['value' => 'o.order_id-DESC', 'text' => 'Последно добавени'],
					['value' => 'o.order_id-ASC', 'text' => 'Най-стари']
				]);
			}
			if (!isset($this->data['pagination_html'])) {
				$this->setData('pagination_html', '');
			}

		} catch (Exception $e) {
			// Ако има грешка, задаваме минимални данни
			$this->setData([
				'orders' => [],
				'status_options' => [['value' => '', 'text' => 'Всички статуси']],
				'sort_options' => [
					['value' => 'o.order_id-DESC', 'text' => 'Последно добавени'],
					['value' => 'o.order_id-ASC', 'text' => 'Най-стари']
				],
				'pagination_html' => '',
				'add_new_url' => $this->getAdminLink('sale/order/add')
			]);
		}

		// Рендиране на шаблона с данните от $this->data
		// Временно използваме тестовия шаблон за debug
		$this->renderTemplateWithDataAndOutput('sale/order');
	}

	public function info() {
		$this->setTitle('Детайли на поръчка');

		// Инициализиране на данните
		$this->initAdminData();

		$subController = $this->setBackendSubController('Sale/Order/Info', $this);

		// Подготовка на данните
		$subController->prepareOrderInfo();

		$this->renderTemplateWithDataAndOutput('sale/order_info');
	}

	public function edit() {
		$this->setTitle('Редакция на поръчка');

		// Инициализиране на данните
		$this->initAdminData();

		$subController = $this->setBackendSubController('Sale/Order/Edit', $this);

		// Подготовка на данните
		$subController->prepareOrderForm();

		$this->renderTemplateWithDataAndOutput('sale/order_form');
	}

	/**
     * Обработва AJAX заявките за автодопълване
     */
    public function autocomplete() {
        $json = [];

        ob_start();

        if ($this->requestGet('type') && $this->requestGet('type')) {
            $type = $this->requestGet('type');

            // Зареждане на съответния субконтролер
            $sub_controller = $this->setBackendSubController('Sale/Order/' . ucfirst($type) . 'Autocomplete', $this);

            if ($sub_controller && method_exists($sub_controller, 'autocomplete')) {
                $json = $sub_controller->autocomplete($this->requestGet());
            } else {
                $json['error'] = 'Методът не е намерен';
            }
        } else {
            $json['error'] = 'Липсващ параметър type';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

	/**
     * Обработва AJAX заявките за промяна на статус
     */
    public function updateStatus() {
        $json = [];

        if ($this->isPostRequest() && $this->hasPermission('modify', 'sale/order')) {
            $order_id = (int)$this->requestPost('order_id');
            $order_status_id = (int)$this->requestPost('order_status_id');
            $comment = $this->requestPost('comment', '');
            $notify = (bool)$this->requestPost('notify', false);

            if ($order_id && $order_status_id) {
                $this->loadModel('sale/order');

                try {
                    $this->model_sale_order->addOrderHistory($order_id, $order_status_id, $comment, $notify);
                    $json['success'] = 'Статусът на поръчката е актуализиран успешно';
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при актуализиране на статуса: ' . $e->getMessage();
                }
            } else {
                $json['error'] = 'Невалидни данни';
            }
        } else {
            $json['error'] = 'Няmate права за тази операция';
        }

        $this->setJSONResponseOutput($json);
    }

	/**
     * Изтрива поръчки
     */
    public function delete() {
        if ($this->isPostRequest() && $this->hasPermission('modify', 'sale/order')) {
            $selected = $this->requestPost('selected', []);

            if (!empty($selected)) {
                $this->loadModel('sale/order');

                foreach ($selected as $order_id) {
                    $this->model_sale_order->deleteOrder((int)$order_id);
                }

                $this->setSession('success', 'Поръчките са изтрити успешно');
            }
        }

        $this->redirectResponse($this->getAdminLink('sale/order'));
    }

}
