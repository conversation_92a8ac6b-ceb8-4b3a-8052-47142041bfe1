<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class CategoryAutocomplete extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }
    
    /**
     * Обработва заявките за автодопълване на категории
     * 
     * @param array $get GET параметри от заявката
     * @return array
     */
    public function autocomplete($get) {
        $json = [];
 

        $this->loadModelAs('catalog/category', 'categoryModel');
        
        $filter_data = [
            'sort'        => 'name',
            'order'       => 'ASC',
            'start'       => 0,
            'limit'       => 10
        ];

        if (isset($get['filter_name'])) {
            $filter_data['filter_name'] = $get['filter_name'];
        }

        $results = $this->categoryModel->getCategories($filter_data);

        foreach ($results as $result) {
            $json[] = [
                'id'   => $result['category_id'],
                'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
            ];
        }
        
        return $json;
    }
}
