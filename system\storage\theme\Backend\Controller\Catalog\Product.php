<?php

namespace Theme25\Backend\Controller\Catalog;

class Product extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'catalog/product');
    }
    
	public function index() {
		$this->setTitle('Продукти');

		// Инициализиране на данните
		$this->initAdminData();

		$subController = $this->setBackendSubController('Catalog/Product/Index', $this);

		// Подготовка на данните
		$subController->prepareData();

		// Рендиране на шаблона с данните от $this->data
		$this->renderTemplateWithDataAndOutput('catalog/product');
	}



	public function edit() {
		$this->setTitle('Редакция на продукт');

		// Инициализиране на данните
		$this->initAdminData();

		$subController = $this->setBackendSubController('Catalog/Product/Edit', $this);

		// Подготовка на данните
		$subController->prepareProductForm();

		$this->renderTemplateWithDataAndOutput('catalog/product_form');
	}

	/**
     * Обработва AJAX заявките за автодопълване
     */
    public function autocomplete() {
        $json = [];

        ob_start();

        if ($this->requestGet('type') && $this->requestGet('type')) {
            $type = $this->requestGet('type');
            
            // Зареждане на съответния субконтролер
            $sub_controller = $this->setBackendSubController('Catalog/Product/' . ucfirst($type) . 'Autocomplete', $this);
            
            if ($sub_controller && method_exists($sub_controller, 'autocomplete')) {
                $json = $sub_controller->autocomplete($this->requestGet());
            } else {
                $json['error'] = 'Методът не е намерен';
            }
        } else {
            $json['error'] = 'Липсващ параметър type';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);

    }

	
}
