<!-- Product Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
<div class="flex flex-col md:flex-row md:items-center justify-between">
<div class="flex items-center">
<a href="{{ back_url }}" data-readdy="true" class="mr-3 text-gray-500 hover:text-primary">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-arrow-left-line ri-lg"></i>
</div>
</a>
<div>
<h1 class="text-2xl font-bold text-gray-800">{{ heading_title }}</h1>
</div>
</div>
<div class="flex items-center space-x-3 mt-4 md:mt-0">
<button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors whitespace-nowrap !rounded-button">
<span>Отказ</span>
</button>
<button id="save-product" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-save-line"></i>
</div>
<span>Запази</span>
</button>
</div>
</div>
</div>
<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
<div class="max-w-7xl">
<div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
<div class="border-b border-gray-200">
<div class="flex overflow-x-auto">
<button class="tab-button active px-6 py-4 text-sm font-medium whitespace-nowrap">Основна информация</button>
<button class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Изображения</button>
<button class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Описание</button>
<button class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Характеристики</button>
<button class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">SEO</button>
</div>
</div>
<!-- Tab Content -->
<div class="p-6">
<!-- Basic Info Tab -->
<div id="tab-basic-info" class="tab-content">
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Име на продукта <span class="text-red-500">*</span></label>
<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете име на продукта">
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Код на продукта <span class="text-red-500">*</span></label>
<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете код на продукта">
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Категория <span class="text-red-500">*</span></label>
<div class="relative">
<select class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm appearance-none pr-10">
<option value="">Изберете категория</option>
<option value="1">Електроника</option>
<option value="2">Мода</option>
<option value="3">Дом и градина</option>
<option value="4">Спорт и свободно време</option>
<option value="5">Красота и здраве</option>
<option value="6">Играчки и детски артикули</option>
</select>
<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
<div class="w-5 h-5 flex items-center justify-center text-gray-400">
<i class="ri-arrow-down-s-line"></i>
</div>
</div>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Марка</label>
<div class="relative">
<select class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm appearance-none pr-10">
<option value="">Изберете марка</option>
<option value="1">Apple</option>
<option value="2">Samsung</option>
<option value="3">Sony</option>
<option value="4">Nike</option>
<option value="5">Adidas</option>
</select>
<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
<div class="w-5 h-5 flex items-center justify-center text-gray-400">
<i class="ri-arrow-down-s-line"></i>
</div>
</div>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Основна цена (лв.) <span class="text-red-500">*</span></label>
<div class="relative">
<input type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="0.00">
<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
<span class="text-gray-500 text-sm">лв.</span>
</div>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Промоционална цена (лв.)</label>
<div class="relative">
<input type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="0.00">
<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
<span class="text-gray-500 text-sm">лв.</span>
</div>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Наличност <span class="text-red-500">*</span></label>
<input type="number" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете количество">
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
<div class="flex items-center space-x-2 mt-2">
<label class="toggle-switch">
<input type="checkbox" checked>
<span class="toggle-slider"></span>
</label>
<span class="text-sm text-gray-700">Активен</span>
</div>
</div>
</div>
</div>
<!-- Images Tab (Hidden by default) -->
<div id="tab-images" class="tab-content hidden">
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Изображения на продукта</label>
<div class="image-upload-area rounded-lg p-8 flex flex-col items-center justify-center cursor-pointer">
<div class="w-16 h-16 flex items-center justify-center text-gray-400 mb-4">
<i class="ri-image-add-line ri-2x"></i>
</div>
<p class="text-sm text-gray-500 mb-1">Плъзнете и пуснете изображения тук</p>
<p class="text-xs text-gray-400">или</p>
<label class="mt-4 px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors cursor-pointer !rounded-button">
<span>Изберете файлове</span>
<input type="file" multiple class="hidden" accept="image/*">
</label>
<p class="text-xs text-gray-400 mt-4">Поддържани формати: JPG, PNG, GIF. Максимален размер: 5MB</p>
</div>
</div>
<div class="mt-8">
<h3 class="text-sm font-medium text-gray-700 mb-4">Качени изображения</h3>
<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
<div class="relative group">
<div class="aspect-square rounded-lg overflow-hidden border border-gray-200">
<img src="https://readdy.ai/api/search-image?query=modern%20minimalist%20product%20photography%20of%20a%20smartphone%20on%20clean%20white%20background%2C%20professional%20e-commerce%20style%2C%20high-end%20product%20shot%2C%20soft%20shadows%2C%20studio%20lighting&width=400&height=400&seq=1&orientation=squarish" alt="Product image" class="w-full h-full object-cover">
</div>
<div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
<button class="p-2 bg-white rounded-full text-gray-700 hover:text-primary">
<div class="w-5 h-5 flex items-center justify-center">
<i class="ri-eye-line"></i>
</div>
</button>
<button class="p-2 bg-white rounded-full text-gray-700 hover:text-primary">
<div class="w-5 h-5 flex items-center justify-center">
<i class="ri-image-edit-line"></i>
</div>
</button>
<button class="p-2 bg-white rounded-full text-red-500 hover:text-red-600">
<div class="w-5 h-5 flex items-center justify-center">
<i class="ri-delete-bin-line"></i>
</div>
</button>
</div>
<div class="absolute top-2 left-2">
<div class="px-2 py-1 bg-primary text-white text-xs rounded-full">Основна</div>
</div>
</div>
<div class="border border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" onclick="document.getElementById('additional-image-upload').click()">
<div class="w-8 h-8 flex items-center justify-center text-gray-400">
<i class="ri-add-line ri-lg"></i>
</div>
<input type="file" id="additional-image-upload" class="hidden" accept="image/*" multiple>
</div>
</div>
</div>
</div>
<!-- Description Tab (Hidden by default) -->
<div id="tab-description" class="tab-content hidden">
<div>
<label class="block text-sm font-medium text-gray-700 mb-2">Кратко описание</label>
<textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете кратко описание на продукта"></textarea>
</div>
<div class="mt-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Пълно описание</label>
<div class="border border-gray-300 rounded overflow-hidden">
<div class="bg-gray-50 border-b border-gray-300 p-2 flex space-x-2">
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-bold"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-italic"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-underline"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-list-unordered"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-list-ordered"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-link"></i>
</div>
</button>
<button class="p-1 hover:bg-gray-200 rounded">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-image-line"></i>
</div>
</button>
</div>
<textarea rows="10" class="w-full px-3 py-2 border-none focus:outline-none focus:ring-0 text-sm" placeholder="Въведете пълно описание на продукта"></textarea>
</div>
</div>
</div>
<!-- Specifications Tab (Hidden by default) -->
<div id="tab-specifications" class="tab-content hidden">
<div class="mb-4 flex justify-between items-center">
<h3 class="text-sm font-medium text-gray-700">Характеристики на продукта</h3>
<button id="add-specification" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-add-line"></i>
</div>
<span>Добави</span>
</button>
</div>
<div id="specifications-container" class="space-y-4">
<div class="specification-item grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg">
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Име</label>
<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Цвят, Размер, Материал">
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Стойност</label>
<div class="flex">
<input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Червен, XL, Памук">
<button class="remove-specification px-3 py-2 bg-red-50 text-red-500 border border-gray-300 border-l-0 rounded-r hover:bg-red-100 transition-colors">
<div class="w-5 h-5 flex items-center justify-center">
<i class="ri-delete-bin-line"></i>
</div>
</button>
</div>
</div>
</div>
</div>
</div>
<!-- SEO Tab (Hidden by default) -->
<div id="tab-seo" class="tab-content hidden">
<div class="space-y-6">
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">URL Slug</label>
<div class="flex">
<span class="inline-flex items-center px-3 text-gray-500 bg-gray-100 border border-r-0 border-gray-300 rounded-l text-sm">example.com/products/</span>
<input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-r focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="product-name">
</div>
<p class="mt-1 text-xs text-gray-500">URL адресът ще бъде генериран автоматично от името на продукта, но можете да го промените.</p>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Meta заглавие</label>
<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете мета заглавие">
<p class="mt-1 text-xs text-gray-500">Препоръчителна дължина: 50-60 символа</p>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Meta описание</label>
<textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете мета описание"></textarea>
<p class="mt-1 text-xs text-gray-500">Препоръчителна дължина: 150-160 символа</p>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Ключови думи</label>
<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете ключови думи, разделени със запетая">
<p class="mt-1 text-xs text-gray-500">Пример: смартфон, телефон, мобилен телефон</p>
</div>
</div>
</div>
</div>
</div>
<!-- Save Button (Fixed at bottom) -->
<div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 flex justify-end">
<div class="max-w-7xl w-full mx-auto flex justify-between items-center">
<div class="text-sm text-gray-500">
<span class="text-red-500">*</span> Задължителни полета
</div>
<div class="flex items-center space-x-3">
<button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors whitespace-nowrap !rounded-button">
<span>Отказ</span>
</button>
<button class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-save-line"></i>
</div>
<span>Запази</span>
</button>
</div>
</div>
</div>
</div>
</main>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
// Tab switching
const tabButtons = document.querySelectorAll('.tab-button');
const tabContents = document.querySelectorAll('.tab-content');
tabButtons.forEach((button, index) => {
button.addEventListener('click', () => {
// Remove active class from all buttons and hide all contents
tabButtons.forEach(btn => btn.classList.remove('active'));
tabContents.forEach(content => content.classList.add('hidden'));
// Add active class to clicked button and show corresponding content
button.classList.add('active');
tabContents[index].classList.remove('hidden');
});
});
// Toggle sidebar
const toggleSidebar = document.getElementById('toggle-sidebar');
const sidebar = document.getElementById('sidebar');
const mobileMenu = document.getElementById('mobile-menu');
toggleSidebar.addEventListener('click', function() {
sidebar.classList.toggle('w-64');
sidebar.classList.toggle('w-20');
const sidebarItems = document.querySelectorAll('.sidebar-item span');
const sidebarLogo = document.getElementById('sidebar-logo');
sidebarItems.forEach(item => {
item.classList.toggle('hidden');
});
sidebarLogo.classList.toggle('hidden');
const icon = toggleSidebar.querySelector('i');
if (icon.classList.contains('ri-menu-fold-line')) {
icon.classList.remove('ri-menu-fold-line');
icon.classList.add('ri-menu-unfold-line');
} else {
icon.classList.remove('ri-menu-unfold-line');
icon.classList.add('ri-menu-fold-line');
}
});
mobileMenu.addEventListener('click', function() {
sidebar.classList.toggle('-translate-x-full');
});
// Image upload area
const imageUploadArea = document.querySelector('.image-upload-area');
const fileInput = imageUploadArea.querySelector('input[type="file"]');
imageUploadArea.addEventListener('dragover', (e) => {
e.preventDefault();
imageUploadArea.classList.add('dragover');
});
imageUploadArea.addEventListener('dragleave', () => {
imageUploadArea.classList.remove('dragover');
});
imageUploadArea.addEventListener('drop', (e) => {
e.preventDefault();
imageUploadArea.classList.remove('dragover');
// Handle file drop (in a real app)
});
imageUploadArea.addEventListener('click', () => {
fileInput.click();
});
fileInput.addEventListener('change', () => {
// Handle file selection (in a real app)
});
// Add specification
const addSpecificationBtn = document.getElementById('add-specification');
const specificationsContainer = document.getElementById('specifications-container');
addSpecificationBtn.addEventListener('click', () => {
const newSpec = document.createElement('div');
newSpec.className = 'specification-item grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg';
newSpec.innerHTML = `
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Име</label>
<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Цвят, Размер, Материал">
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-1">Стойност</label>
<div class="flex">
<input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Червен, XL, Памук">
<button class="remove-specification px-3 py-2 bg-red-50 text-red-500 border border-gray-300 border-l-0 rounded-r hover:bg-red-100 transition-colors">
<div class="w-5 h-5 flex items-center justify-center">
<i class="ri-delete-bin-line"></i>
</div>
</button>
</div>
</div>
`;
specificationsContainer.appendChild(newSpec);
// Add event listener to the new remove button
const removeBtn = newSpec.querySelector('.remove-specification');
removeBtn.addEventListener('click', () => {
newSpec.remove();
});
});
// Remove specification (for initial item)
document.querySelectorAll('.remove-specification').forEach(btn => {
btn.addEventListener('click', () => {
btn.closest('.specification-item').remove();
});
});
// Save product button
const saveProductBtn = document.getElementById('save-product');
saveProductBtn.addEventListener('click', () => {
// In a real app, this would validate and submit the form
alert('Продуктът е запазен успешно!');
});
});
</script>