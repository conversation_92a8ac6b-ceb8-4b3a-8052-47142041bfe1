<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Index extends \Theme25\ControllerSubMethods {

    public function prepareData() {

        $this->prepareProductListData()
			 ->prepareFilterOptions()
			 ->prepareProductItems()
			 ->preparePagination();

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product')
        ]);
    }

    /**
	 * Подготвя основните данни за списъка с продукти
	 *
	 * @return $this За верижно извикване на методи
	 */
	private function prepareProductListData() {
		// Зареждане на необходимите модели
		$this->loadModelsAs([
			'catalog/product' => 'products',
			'tool/image' => 'image'
		]);

		// URL адреси - използваме getAdminLinks за групово извличане
		$routes = [
			'add_new_url' => 'catalog/product/add',
			'delete_url' => 'catalog/product/delete&product_id=PRODUCT_ID',
			'edit_url' => 'catalog/product/edit&product_id=PRODUCT_ID',
			'copy_url' => 'catalog/product/copy&product_id=PRODUCT_ID'
		];

		// Добавяне на URL адресите към данните
		$this->setData($this->getAdminLinks($routes));

		return $this;
	}

	/**
	 * Подготвя опциите за филтриране и сортиране
	 *
	 * @return $this За верижно извикване на методи
	 */
	private function prepareFilterOptions() {
		// Получаване на данните за филтрите
		$filter_data = $this->getFilterData();

		// Опции за сортиране
		$sort_options = [
			['value' => 'p.date_added-DESC', 'text' => 'Последно добавени'],
			['value' => 'p.price-ASC', 'text' => 'Цена (възх.)'],
			['value' => 'p.price-DESC', 'text' => 'Цена (низх.)'],
			['value' => 'pd.name-ASC', 'text' => 'Име (А-Я)'],
			['value' => 'pd.name-DESC', 'text' => 'Име (Я-А)']
		];

		// Опции за брой продукти на страница
		// $limits_array = [
		// 	['value' => 10, 'text' => '10 на страница'],
		// 	['value' => 20, 'text' => '20 на страница'],
		// 	['value' => 50, 'text' => '50 на страница'],
		// 	['value' => 100, 'text' => '100 на страница']
		// ];

		// URL адреси за филтри - използваме getAdminLinks за групово извличане
		$filter_urls = $this->getAdminLinks([
			'sort_url' => 'catalog/product',
			'limit_url' => 'catalog/product',
			'filter_active_url' => 'catalog/product',
			'filter_special_url' => 'catalog/product'
		], [
			'sort_url' => '&sort=SORT_VALUE',
			'limit_url' => '&limit=LIMIT_VALUE',
			'filter_active_url' => '&filter_status=FILTER_VALUE',
			'filter_special_url' => '&filter_special=FILTER_VALUE'
		]);

		// Текущи филтри
		$current_filters = [
			'filter_active' => isset($filter_data['filter_status']) && $filter_data['filter_status'] == 1,
			'filter_special' => isset($filter_data['filter_special']) && $filter_data['filter_special'] == 1,
			'view_type' => $this->requestGet('view') ?: 'grid',
			'limit' => $filter_data['limit']
		];

		// Добавяне на данните към $this->data с един метод
		$this->setData([
			'sort_options' => $sort_options,
			// 'limits_array' => $limits_array,
			'filter_data' => $filter_data
		])
		->setData($filter_urls)
		->setData($current_filters);

		return $this;
	}

	/**
	 * Подготвя списъка с продукти
	 *
	 * @return $this За верижно извикване на методи
	 */
	private function prepareProductItems() {
		// Получаване на данните за филтрите
		$filter_data = $this->data['filter_data'];

		// Получаване на продуктите
		$results = $this->products->getProducts($filter_data);

		// Подготовка на данните за продуктите
		$products = [];

		foreach ($results as $result) {
			// Проверка за специална цена
			$special_data = $this->getSpecialPriceData($result);

			// Подготовка на данните за продукта
			$products[] = [
				'product_id' => $result['product_id'],
				'name' => $result['name'],
				'model' => $result['model'],
				'price' => $this->formatCurrency($special_data['price'], $this->getConfig('config_currency')),
				'old_price' => $special_data['old_price'],
				'discount_percent' => $special_data['discount_percent'],
				'special' => $special_data['special'],
				'status' => $result['status'],
				'category' => $this->getCategoryName($result['product_id']),
				'edit' => str_replace('PRODUCT_ID', $result['product_id'], $this->data['edit_url']),
			];
		}

		// Добавяне на продуктите към данните
		$this->setData('products', $products);

		return $this;
	}

	/**
	 * Подготвя пагинацията
	 *
	 * @return $this За верижно извикване на методи
	 */
	private function preparePagination() {
		// Получаване на данните за филтрите
		$filter_data = $this->data['filter_data'];

		// Получаване на общия брой продукти
		$product_total = $this->products->getTotalProducts($filter_data);

		// Опции за брой продукти на страница
		$limits = [
			['value' => 10, 'text' => '10 на страница'],
			['value' => 20, 'text' => '20 на страница'],
			['value' => 50, 'text' => '50 на страница'],
			['value' => 100, 'text' => '100 на страница']
		];

		// Създаване и конфигуриране на обект за пагинация
		$pagination = new \Theme25\Pagination();
		$pagination->total = $product_total;
		$pagination->page = $filter_data['page'];
		$pagination->limit = $filter_data['limit'];
		$pagination->url = $this->getAdminLink('catalog/product', '&page={page}');
		$pagination->setLimits($limits);
		$pagination->setLimitUrl($this->getAdminLink('catalog/product', '&limit={limit}'));
		$pagination->setProductText('продукта');

		// Генериране на HTML код за цялата пагинация
		$this->setData('pagination_html', $pagination->render());

		return $this;
	}

	/**
	 * Получава данните за филтрите от заявката
	 *
	 * @return array Масив с данни за филтрите
	 */
	private function getFilterData() {
		// Филтри
		$filter_data = [];

		// Сортиране
		$sort = $this->requestGet('sort') ?: 'pd.name';
		$order = $this->requestGet('order') ?: 'ASC';

		// Ако сортирането е във формат 'field-ORDER'
		if (strpos($sort, '-') !== false) {
			list($sort, $order) = explode('-', $sort);
		}

		$filter_data['sort'] = $sort;
		$filter_data['order'] = $order;

		// Пагинация
		$page = (int)$this->requestGet('page') ?: 1;
		//$limit = (int)$this->requestGet('limit') ? $this->requestGet('limit') : $this->getConfig('config_limit_admin');
		$limit = (int)$this->requestGet('limit') ? $this->requestGet('limit') : 20;

		$filter_data['start'] = ($page - 1) * $limit;
		$filter_data['limit'] = $limit;
		$filter_data['page'] = $page;

		// Филтри
		$filter_fields = [
			'filter_name',
			'filter_model',
			'filter_price',
			'filter_price_min',
			'filter_price_max',
			'filter_quantity',
			'filter_status',
			'filter_special',
			'filter_category_id',
			'filter_date_added_start',
			'filter_date_added_end'
		];

		foreach ($filter_fields as $field) {
			if ($value = $this->requestGet($field)) {
				$filter_data[$field] = $value;
			}
		}

		return $filter_data;
	}

	/**
	 * Получава данните за специалната цена на продукта
	 *
	 * @param array $product Данни за продукта
	 * @return array Масив с данни за специалната цена
	 */
	private function getSpecialPriceData($product) {
		$data = [
			'special' => false,
			'price' => $product['price'],
			'old_price' => '',
			'discount_percent' => ''
		];

		if ($product['special'] && $product['special'] < $product['price']) {
			$data['special'] = true;
			$data['price'] = $product['special'];
			$data['old_price'] = $this->formatCurrency($product['price'], $this->getConfig('config_currency'));
			$data['discount_percent'] = '-' . round(100 - ($product['special'] / $product['price'] * 100)) . '%';
		}

		return $data;
	}

	/**
	 * Получава името на категорията на продукта
	 *
	 * @param int $product_id ID на продукта
	 * @return string Име на категорията
	 */
	private function getCategoryName($product_id) {
		// Зареждане на модела за категории, ако не е зареден
		if (!isset($this->categories)) {
			$this->loadModelAs('catalog/category', 'categories');
		}

		$product_categories = $this->products->getProductCategories($product_id);

		if (!empty($product_categories)) {
			$category_id = reset($product_categories);
			$category_info = $this->categories->getCategory($category_id);

			if ($category_info) {
				return $category_info['name'];
			}
		}

		return 'Без категория';
	}

}