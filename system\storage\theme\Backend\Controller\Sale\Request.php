<?php

namespace Theme25\Backend\Controller\Sale;

class Request extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'sale/request');
    }

    public function index() {
        $this->setTitle('Запитвания от клиенти');

        // Инициализиране на данните
        $this->initAdminData();

        // Подготовка на данните с верижно извикване на методи
        $this->prepareRequestListData()
             ->prepareFilterOptions()
             ->prepareRequestItems()
             ->preparePagination();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('sale/request');
    }

    /**
     * Подготвя основните данни за списъка със запитвания
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareRequestListData() {
        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'sale/request' => 'requests' 
        ]);

        // URL адреси - използваме getAdminLinks за групово извличане
        $routes = [
            // 'add_new_url' => 'sale/request/add', // Ще се добави по-късно, ако е необходимо
            'delete_url' => 'sale/request/delete&request_id=REQUEST_ID',
            'view_url'   => 'sale/request/view&request_id=REQUEST_ID' // За преглед/отговор
        ];

        // Добавяне на URL адресите към данните
        $this->setData($this->getAdminLinks($routes));
        $this->setData('form_action', $this->getAdminLink('sale/request/delete')); // Добавено
        $this->setData('filter_url', $this->getAdminLink('sale/request')); // Добавено

        return $this;
    }

    /**
     * Подготвя опциите за филтриране
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareFilterOptions() {
        // Получаване на данните за филтрите
        $filter_data = $this->getFilterData();

        // Опции за статус
        $status_options = [
            ['value' => '', 'text' => 'Всички статуси'],
            ['value' => '1', 'text' => 'Нов'], // Примерни стойности, трябва да съответстват на базата данни
            ['value' => '2', 'text' => 'Отговорен'],
            ['value' => '3', 'text' => 'Приключен'],
            ['value' => '4', 'text' => 'Изчакващ']
        ];

        // URL адреси за филтри
        $filter_urls = $this->getAdminLinks([
            'filter_url' => 'sale/request',
            'limit_url' => 'sale/request'
        ], [
            'limit_url' => '&limit=LIMIT_VALUE'
        ]);

        // Текущи филтри
        $current_filters = [
            'filter_status' => $filter_data['filter_status'],
            'filter_date_start' => $filter_data['filter_date_start'],
            'filter_date_end' => $filter_data['filter_date_end'],
            'filter_name' => $filter_data['filter_name'],
            'filter_email' => $filter_data['filter_email'],
            'filter_subject' => $filter_data['filter_subject'],
            'limit' => $filter_data['limit']
        ];

        // Добавяне на данните към $this->data
        $this->setData($filter_data);
        $this->setData('status_options', $status_options);

        return $this;
    }

    /**
     * Подготвя списъка със запитвания
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareRequestItems() {
        // Получаване на данните за филтрите
        $filter_data = $this->getData('filter_data');

        // Получаване на запитванията
        $results = $this->requests->getRequests($filter_data);

        // Подготовка на данните за запитванията
        $requests_data = [];

        // Дефиниране на текстови репрезентации на статусите
        $status_texts = [
            1 => ['text' => 'Нов', 'class' => 'bg-blue-100 text-blue-800'],
            2 => ['text' => 'Отговорен', 'class' => 'bg-green-100 text-green-800'],
            3 => ['text' => 'Приключен', 'class' => 'bg-gray-100 text-gray-800'],
            4 => ['text' => 'Изчакващ', 'class' => 'bg-yellow-100 text-yellow-800'],
            // Добавете още статуси при нужда
        ];

        foreach ($results as $result) {
            $status_info = isset($status_texts[$result['status_id']]) ? $status_texts[$result['status_id']] : ['text' => 'Неизвестен', 'class' => 'bg-red-100 text-red-800'];

            $requests_data[] = [
                'request_id'    => $result['request_id'],
                'customer_name' => $result['customer_name'],
                'email'         => $result['email'],
                'subject'       => $result['subject'],
                'date_added'    => date('d.m.Y H:i', strtotime($result['date_added'])),
                'status_text'   => $status_info['text'],
                'status_class'  => $status_info['class'],
                'view'          => str_replace('REQUEST_ID', $result['request_id'], $this->data['view_url']),
                'delete'        => str_replace('REQUEST_ID', $result['request_id'], $this->data['delete_url'])
            ];
        }

        // Добавяне на запитванията към данните
        $this->setData('requests', $requests_data);

        return $this;
    }

    /**
     * Подготвя пагинацията
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePagination() {
        // Получаване на данните за филтрите
        $filter_data = $this->getData('filter_data');

        // Получаване на общия брой запитвания
        $request_total = $this->requests->getTotalRequests($filter_data);

        // Опции за брой запитвания на страница
        $limits = [
            ['value' => 10, 'text' => '10 на страница'],
            ['value' => 20, 'text' => '20 на страница'],
            ['value' => 50, 'text' => '50 на страница'],
            ['value' => 100, 'text' => '100 на страница']
        ];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $request_total;
        $pagination->page = $filter_data['page'];
        $pagination->limit = $filter_data['limit'];
        $pagination->url = $this->getAdminLink('sale/request', $this->buildFilterQueryString($filter_data, ['page']) . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('sale/request', $this->buildFilterQueryString($filter_data, ['limit', 'page']) . '&limit={limit}'));
        $pagination->setProductText('запитвания');

        // Генериране на HTML код за цялата пагинация
        $this->setData('pagination_html', $pagination->render());

        return $this;
    }

    /**
     * Получава данните за филтрите от заявката
     *
     * @return array Масив с данни за филтрите
     */
    private function getFilterData() {
        return [
            'filter_status'     => $this->requestGet('filter_status', ''),
            'filter_date_start' => $this->requestGet('filter_date_start', ''),
            'filter_date_end'   => $this->requestGet('filter_date_end', ''),
            'filter_name'       => $this->requestGet('filter_name', ''),
            'filter_email'      => $this->requestGet('filter_email', ''),
            'filter_subject'    => $this->requestGet('filter_subject', ''),
            'page'              => $this->requestGet('page', 1),
            'limit'             => $this->requestGet('limit', $this->config->get('config_limit_admin'))
        ];
    }
    
    /**
     * Изгражда низ за заявка от данните за филтъра, като изключва определени ключове.
     *
     * @param array $filter_data Данни за филтъра.
     * @param array $exclude_keys Ключове за изключване.
     * @return string Низ за заявка.
     */
    private function buildFilterQueryString(array $filter_data, array $exclude_keys = []) {
        $query_params = [];
        foreach ($filter_data as $key => $value) {
            if (!in_array($key, $exclude_keys) && !empty($value)) {
                $query_params[$key] = $value;
            }
        }
        return http_build_query($query_params);
    }


    
    /**
     * Метод за преглед/отговор на запитване (замества edit)
     */
    public function view() {
        $this->setTitle('Преглед на запитване');

        $this->initAdminData();

        $request_id = $this->requestGet('request_id', 0);
        $request_info = $this->requests->getRequest($request_id);

        if (!$request_info) {
            $this->setSession('error', 'Внимание: Запитването не е намерено!');
            $this->redirectResponse($this->getAdminLink('sale/request'));
        }

        // Зареждане на данни за запитването и евентуални отговори
        $this->setData('request_info', $request_info);
        

        // URL за изпращане на отговор
        $this->setData('action_reply', $this->getAdminLink('sale/request/reply&request_id=' . $request_id));
        $this->setData('cancel', $this->getAdminLink('sale/request'));

        $this->renderTemplateWithDataAndOutput('sale/request_form'); // Нов шаблон за формата за преглед/отговор
    }

    /**
     * Метод за изтриване на запитване
     */
    public function delete() {
        if ($this->requestPost('selected') && $this->validateDelete()) {
            foreach ($this->requestPost('selected') as $request_id) {
                $this->requests->deleteRequest($request_id);
            }
            $this->setSession('success', 'Успех: Изтрихте избраните запитвания!');
            $this->redirectResponse($this->getAdminLink('sale/request', $this->buildFilterQueryString($this->getFilterData(), ['page', 'limit']) . '&page=' . $this->requestGet('page',1) . '&limit=' . $this->requestGet('limit',$this->config->get('config_limit_admin'))));
        } elseif ($this->requestGet('request_id') && $this->validateDelete()) {
            $this->requests->deleteRequest($this->requestGet('request_id'));
            $this->setSession('success', 'Успех: Изтрихте избраните запитвания!');
            $this->redirectResponse($this->getAdminLink('sale/request', $this->buildFilterQueryString($this->getFilterData(), ['page', 'limit']) . '&page=' . $this->requestGet('page',1) . '&limit=' . $this->requestGet('limit',$this->config->get('config_limit_admin'))));
        } else {
            $this->setSession('error', $this->getSession('error') ? $this->getSession('error') : 'Внимание: Нямате права за модифициране на запитвания!');
            $this->redirectResponse($this->getAdminLink('sale/request'));
        }
    }
    
    /**
     * Метод за валидиране на правата за изтриване
     */
    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'sale/request')) {
            $this->setSession('error', 'Внимание: Нямате права за модифициране на запитвания!');
            return false;
        }
        return true;
    }
}
