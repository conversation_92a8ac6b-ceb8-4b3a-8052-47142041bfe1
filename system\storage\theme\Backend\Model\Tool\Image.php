<?php

namespace Theme25\Backend\Model\Tool;

class Image extends \Model {
	public function resize($filename, $width, $height, $theme_path = true) {
		// Увеличаваме лимита на паметта за обработка на големи изображения
		ini_set('memory_limit', '256M');

        $dir_image = $this->getImagePath($theme_path);
        $no_image = ThemeData()->getNoImageFile($theme_path);

        // F()->log->developer($filename, __FILE__, __LINE__);

        if (!is_file($dir_image . $filename) || substr(str_replace('\\', '/', realpath($dir_image . $filename)), 0, strlen($dir_image)) != str_replace('\\', '/', $dir_image)) {
            if($no_image) {
                $filename = $no_image;
            }
            else {
                return;
            }
		}

		$extension = pathinfo($filename, PATHINFO_EXTENSION);

		$image_old = $filename;
		$image_new = 'cache/' . utf8_substr($filename, 0, utf8_strrpos($filename, '.')) . '-' . $width . 'x' . $height . '.' . $extension;

		if (!is_file($dir_image . $image_new) || (filemtime($dir_image . $image_old) > filemtime($dir_image . $image_new))) {
			list($width_orig, $height_orig, $image_type) = getimagesize($dir_image . $image_old);
			if (!in_array($image_type, array(IMAGETYPE_PNG, IMAGETYPE_JPEG, IMAGETYPE_GIF))) {
				return $dir_image . $image_old;
			}

			$path = '';

			$directories = explode('/', dirname($image_new));

			foreach ($directories as $directory) {
				$path = $path . '/' . $directory;

				if (!is_dir($dir_image . $path)) {
					@mkdir($dir_image . $path, 0777);
				}
			}

			if ($width_orig != $width || $height_orig != $height) {
				try {
					$image = new \Theme25\ImageEditor();
					$image->setImagePath($dir_image . $image_old);
					// ImageEditor автоматично запазва файла и връща пътя
					$resized_file = $image->resize($width, $height);
					// Преименуваме файла до желаното местоположение
					rename($resized_file, $dir_image . $image_new);
				} catch (\Exception $e) {
					// Ако има грешка, записваме я в лог файла и използваме стандартния метод за копиране
					F()->log->error("ImageEditor error: " . $e->getMessage(), __FILE__, __LINE__);
					copy($dir_image . $image_old, $dir_image . $image_new);
				}
			} else {
				copy($dir_image . $image_old, $dir_image . $image_new);
			}
		}

		return ThemeData()->getImageWebUrl($theme_path) . $image_new;
	}

    private function getImagePath($theme_path = true) {
        return $theme_path ? ThemeData()->getImageServerPath() : DIR_IMAGE;
    }
}
