<?php

namespace Theme25\Backend\Controller\Sale\Order;

class Index extends \Theme25\ControllerSubMethods {

    public function prepareData() {

        $this->prepareOrderListData()
			 ->prepareFilterOptions()
			 ->prepareOrderItems()
			 ->preparePagination();

        $this->setData([
            'back_url' => $this->getAdminLink('sale/order')
        ]);
    }

    /**
	 * Подготвя основните данни за списъка с поръчки
	 *
	 * @return $this За верижно извикване на методи
	 */
	private function prepareOrderListData() {
		// Зареждане на необходимите модели
		try {
			$this->loadModelsAs([
				'sale/order' => 'orders',
				'localisation/order_status' => 'orderStatuses'
			]);
		} catch (Exception $e) {
			// Ако моделите не могат да се заредят, задаваме празни данни
			$this->setData([
				'orders' => [],
				'status_options' => [['value' => '', 'text' => 'Всички статуси']],
				'sort_options' => [],
				'pagination_html' => ''
			]);
			return $this;
		}

		// URL адреси - използваме getAdminLinks за групово извличане
		$routes = [
			'add_new_url' => 'sale/order/add',
			'delete_url' => 'sale/order/delete&order_id=ORDER_ID',
			'edit_url' => 'sale/order/edit&order_id=ORDER_ID',
			'info_url' => 'sale/order/info&order_id=ORDER_ID',
			'invoice_url' => 'sale/order/invoice&order_id=ORDER_ID',
			'shipping_url' => 'sale/order/shipping&order_id=ORDER_ID'
		];

		// Добавяне на URL адресите към данните
		$this->setData($this->getAdminLinks($routes));

		return $this;
	}

	/**
	 * Подготвя опциите за филтриране и сортиране
	 *
	 * @return $this За верижно извикване на методи
	 */
	private function prepareFilterOptions() {
		// Получаване на данните за филтрите
		$filter_data = $this->getFilterData();

		// Опции за сортиране
		$sort_options = [
			['value' => 'o.order_id-DESC', 'text' => 'Последно добавени'],
			['value' => 'o.order_id-ASC', 'text' => 'Най-стари'],
			['value' => 'o.total-ASC', 'text' => 'Сума (възх.)'],
			['value' => 'o.total-DESC', 'text' => 'Сума (низх.)'],
			['value' => 'customer-ASC', 'text' => 'Клиент (А-Я)'],
			['value' => 'customer-DESC', 'text' => 'Клиент (Я-А)']
		];

		// URL адреси за филтри - използваме getAdminLinks за групово извличане
		$filter_urls = $this->getAdminLinks([
			'sort_url' => 'sale/order',
			'limit_url' => 'sale/order',
			'filter_status_url' => 'sale/order',
			'filter_customer_url' => 'sale/order'
		], [
			'sort_url' => '&sort=SORT_VALUE',
			'limit_url' => '&limit=LIMIT_VALUE',
			'filter_status_url' => '&filter_order_status_id=FILTER_VALUE',
			'filter_customer_url' => '&filter_customer=FILTER_VALUE'
		]);

		// Получаване на статусите на поръчки
		$status_options = [['value' => '', 'text' => 'Всички статуси']];
		try {
			if (isset($this->orderStatuses)) {
				$order_statuses = $this->orderStatuses->getOrderStatuses();
				foreach ($order_statuses as $status) {
					$status_options[] = [
						'value' => $status['order_status_id'],
						'text' => $status['name']
					];
				}
			}
		} catch (Exception $e) {
			// Ако има грешка при зареждане на статусите, използваме само основния
		}

		// Текущи филтри
		$current_filters = [
			'filter_order_status_id' => $filter_data['filter_order_status_id'] ?? '',
			'filter_customer' => $filter_data['filter_customer'] ?? '',
			'filter_date_added_start' => $filter_data['filter_date_added_start'] ?? '',
			'filter_date_added_end' => $filter_data['filter_date_added_end'] ?? '',
			'limit' => $filter_data['limit']
		];

		// Добавяне на данните към $this->data с един метод
		$this->setData([
			'sort_options' => $sort_options,
			'status_options' => $status_options,
			'filter_data' => $filter_data
		])
		->setData($filter_urls)
		->setData($current_filters);

		return $this;
	}

	/**
	 * Подготвя списъка с поръчки
	 *
	 * @return $this За верижно извикване на методи
	 */
	private function prepareOrderItems() {
		// Получаване на данните за филтрите
		$filter_data = $this->data['filter_data'] ?? [];

		// Подготовка на данните за поръчките
		$orders = [];


		try {
			// if (!empty($this->orders)) {
				// Получаване на поръчките
				$results = $this->orders->getOrders($filter_data);

				foreach ($results as $result) {
					// Подготовка на данните за поръчката
					$orders[] = [
						'order_id' => $result['order_id'] ?? '',
						'customer' => $result['customer'] ?? 'Неизвестен клиент',
						'email' => $result['email'] ?? '',
						'order_status' => $result['order_status'] ?? 'Неизвестен',
						'order_status_id' => $result['order_status_id'] ?? 0,
						'payment_method' => $result['payment_method'] ?? '',
						'shipping_code' => $result['shipping_code'] ?? '',
						'total' => $this->formatCurrency($result['total'] ?? 0, $result['currency_code'] ?? 'BGN', $result['currency_value'] ?? 1),
						'date_added' => $this->formatDate($result['date_added'] ?? ''),
						'date_modified' => $this->formatDate($result['date_modified'] ?? ''),
						'info' => str_replace('ORDER_ID', $result['order_id'] ?? '', $this->data['info_url'] ?? ''),
						'edit' => str_replace('ORDER_ID', $result['order_id'] ?? '', $this->data['edit_url'] ?? ''),
						'invoice' => str_replace('ORDER_ID', $result['order_id'] ?? '', $this->data['invoice_url'] ?? ''),
						'shipping' => str_replace('ORDER_ID', $result['order_id'] ?? '', $this->data['shipping_url'] ?? ''),
					];
				}
			// }
		} catch (Exception $e) {
			// Ако има грешка при зареждане на поръчките, оставяме празен масив
		}

		// Добавяне на поръчките към данните
		$this->setData('orders', $orders);

		return $this;
	}

	/**
	 * Подготвя пагинацията
	 *
	 * @return $this За верижно извикване на методи
	 */
	private function preparePagination() {
		// Получаване на данните за филтрите
		$filter_data = $this->data['filter_data'] ?? [];

		$pagination_html = '';

		try {
			// if (isset($this->orders)) {
				// Получаване на общия брой поръчки
				$order_total = $this->orders->getTotalOrders($filter_data);

				// Опции за брой поръчки на страница
				$limits = [
					['value' => 10, 'text' => '10 на страница'],
					['value' => 20, 'text' => '20 на страница'],
					['value' => 50, 'text' => '50 на страница'],
					['value' => 100, 'text' => '100 на страница']
				];

				// Създаване и конфигуриране на обект за пагинация
				if (class_exists('\Theme25\Pagination')) {
					$pagination = new \Theme25\Pagination();
					$pagination->total = $order_total;
					$pagination->page = $filter_data['page'] ?? 1;
					$pagination->limit = $filter_data['limit'] ?? 20;
					$pagination->url = $this->getAdminLink('sale/order', '&page={page}');
					$pagination->setLimits($limits);
					$pagination->setLimitUrl($this->getAdminLink('sale/order', '&limit={limit}'));
					$pagination->setProductText('поръчки');

					// Генериране на HTML код за цялата пагинация
					$pagination_html = $pagination->render();
				}
			// }
		} catch (Exception $e) {
			// Ако има грешка при пагинацията, оставяме празен HTML
		}

		$this->setData('pagination_html', $pagination_html);

		return $this;
	}

	/**
	 * Получава данните за филтрите от заявката
	 *
	 * @return array Масив с данни за филтрите
	 */
	private function getFilterData() {
		// Филтри
		$filter_data = [];

		// Сортиране
		$sort = $this->requestGet('sort') ?: 'o.order_id';
		$order = $this->requestGet('order') ?: 'DESC';

		// Ако сортирането е във формат 'field-ORDER'
		if (strpos($sort, '-') !== false) {
			list($sort, $order) = explode('-', $sort);
		}

		$filter_data['sort'] = $sort;
		$filter_data['order'] = $order;

		// Пагинация
		$page = (int)$this->requestGet('page') ?: 1;
		$limit = (int)$this->requestGet('limit') ? $this->requestGet('limit') : 20;

		$filter_data['start'] = ($page - 1) * $limit;
		$filter_data['limit'] = $limit;
		$filter_data['page'] = $page;

		// Филтри
		$filter_fields = [
			'filter_order_id',
			'filter_customer',
			'filter_order_status_id',
			'filter_total',
			'filter_date_added_start',
			'filter_date_added_end',
			'filter_payment_method'
		];

		foreach ($filter_fields as $field) {
			if ($value = $this->requestGet($field)) {
				$filter_data[$field] = $value;
			}
		}

		return $filter_data;
	}

	/**
	 * Получава статус класа за визуализация
	 *
	 * @param int $status_id ID на статуса
	 * @return string CSS клас за статуса
	 */
	private function getStatusClass($status_id) {
		$status_classes = [
			1 => 'status-new',      // Нова
			2 => 'status-processing', // В процес
			5 => 'status-completed',  // Изпълнена
			7 => 'status-cancelled',  // Отказана
		];

		return $status_classes[$status_id] ?? 'status-unknown';
	}

}
