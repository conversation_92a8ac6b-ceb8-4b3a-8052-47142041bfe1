
<div id="content" class="flex-1 flex flex-col overflow-hidden">
  <!-- Header -->
  <header class="bg-white border-b border-gray-200">
    <div class="flex items-center justify-between p-4">
      <div class="flex items-center">
        <button id="mobile-menu" class="mr-4 md:hidden text-gray-500 hover:text-primary">
          <div class="w-8 h-8 flex items-center justify-center">
            <i class="ri-menu-line ri-lg"></i>
          </div>
        </button>
        <h1 class="text-xl font-semibold text-gray-700">{{ heading_title }}</h1>
      </div>
      <div class="flex items-center space-x-4">
        <button type="button" onclick="$('#form-request-delete').submit();" data-bs-toggle="tooltip" title="Изтрий избраните" class="bg-red-500 text-white px-4 py-2 rounded-button hover:bg-red-600 transition-colors whitespace-nowrap">
            <i class="ri-delete-bin-line mr-2"></i>Изтрий
        </button>
        <a href="{{ link_home }}" class="text-gray-500 hover:text-primary">
          <div class="w-8 h-8 flex items-center justify-center">
            <i class="ri-home-4-line ri-lg"></i>
          </div>
        </a>
        <a href="{{ logout_url }}" class="text-gray-500 hover:text-primary">
          <div class="w-8 h-8 flex items-center justify-center">
            <i class="ri-logout-box-r-line ri-lg"></i>
          </div>
        </a>
      </div>
    </div>
    <!-- Breadcrumbs -->
    <div class="px-4 py-2 bg-gray-50 border-b border-gray-200">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                {% for breadcrumb in breadcrumbs %}
                <li class="inline-flex items-center">
                    {% if breadcrumb.href %}
                    <a href="{{ breadcrumb.href }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary">
                        {% if loop.first %}<i class="ri-home-5-fill mr-2"></i>{% endif %}
                        {{ breadcrumb.text }}
                    </a>
                    {% else %}
                    <span class="inline-flex items-center text-sm font-medium text-gray-500 ms-1 md:ms-2">
                         {% if loop.first %}<i class="ri-home-5-fill mr-2"></i>{% endif %}
                        {{ breadcrumb.text }}
                    </span>
                    {% endif %}
                </li>
                {% if not loop.last %}
                <li>
                    <div class="flex items-center">
                        <i class="ri-arrow-right-s-line text-gray-400"></i>
                    </div>
                </li>
                {% endif %}
                {% endfor %}
            </ol>
        </nav>
    </div>
  </header>

  <!-- Main Content Area -->
  <main class="flex-1 overflow-y-auto p-6 bg-gray-50">
    {% if error_warning %}
    <div class="mb-4 p-4 bg-red-100 text-red-700 border border-red-200 rounded-lg shadow-sm flex items-center">
        <i class="ri-error-warning-line ri-lg mr-3"></i>
        <div>{{ error_warning }}</div>
        <button type="button" class="ml-auto text-red-500 hover:text-red-700" onclick="this.parentElement.remove();">
            <i class="ri-close-line ri-lg"></i>
        </button>
    </div>
    {% endif %}
    {% if success %}
    <div class="mb-4 p-4 bg-green-100 text-green-700 border border-green-200 rounded-lg shadow-sm flex items-center">
        <i class="ri-check-double-line ri-lg mr-3"></i>
        <div>{{ success }}</div>
        <button type="button" class="ml-auto text-green-500 hover:text-green-700" onclick="this.parentElement.remove();">
            <i class="ri-close-line ri-lg"></i>
        </button>
    </div>
    {% endif %}

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form id="form-filter" method="get" action="{{ filter_url }}">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="filter-status" class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
                    <select id="filter-status" name="filter_status" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                        {% for option in status_options %}
                            <option value="{{ option.value }}" {% if option.value == filter_status %}selected{% endif %}>{{ option.text }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label for="filter-date-start" class="block text-sm font-medium text-gray-700 mb-1">От дата</label>
                    <input type="text" id="filter-date-start" name="filter_date_start" value="{{ filter_date_start }}" placeholder="дд.мм.гггг" class="date-picker bg-white border border-gray-300 rounded-button py-2 px-3 w-full focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
                <div>
                    <label for="filter-date-end" class="block text-sm font-medium text-gray-700 mb-1">До дата</label>
                    <input type="text" id="filter-date-end" name="filter_date_end" value="{{ filter_date_end }}" placeholder="дд.мм.гггг" class="date-picker bg-white border border-gray-300 rounded-button py-2 px-3 w-full focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
                <div class="flex space-x-2">
                    <button type="submit" class="w-full bg-primary text-white px-4 py-2 rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap text-sm">
                        <i class="ri-filter-3-line mr-1"></i> Приложи филтри
                    </button>
                    <button type="button" onclick="$('#filter-modal').removeClass('hidden').addClass('flex'); document.body.style.overflow='hidden';" class="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-button hover:bg-gray-300 transition-colors whitespace-nowrap text-sm">
                        <i class="ri-sound-module-line mr-1"></i> Разширени
                    </button>
                </div>
            </div>
        </form>
    </div>

    <form action="{{ form_action }}" method="post" id="form-request-delete">
    <div class="bg-white rounded-lg shadow-md overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="p-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Име</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Имейл</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Дата</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Тема</th>
            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {% if requests %}
            {% for request_item in requests %}
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="p-4 whitespace-nowrap">
                <input type="checkbox" name="selected[]" value="{{ request_item.request_id }}" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                    <i class="ri-user-line"></i>
                  </div>
                  <div class="text-sm font-medium text-gray-900">{{ request_item.customer_name }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ request_item.email }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ request_item.date_added }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ request_item.subject }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ request_item.status_class }}">{{ request_item.status_text }}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-right space-x-2">
                <a href="{{ request_item.view }}" class="text-primary hover:text-primary/80" title="Преглед">
                  <div class="w-8 h-8 inline-flex items-center justify-center rounded-button hover:bg-primary/10">
                    <i class="ri-eye-line"></i>
                  </div>
                </a>
                {# <button class="text-blue-500 hover:text-blue-700 reply-inquiry" data-id="{{ request_item.request_id }}" title="Отговор">
                  <div class="w-8 h-8 inline-flex items-center justify-center rounded-button hover:bg-blue-500/10">
                    <i class="ri-reply-line"></i>
                  </div>
                </button> #}
                <a href="{{ request_item.delete }}" onclick="return confirm('Сигурни ли сте, че искате да изтриете това запитване?');" class="text-red-500 hover:text-red-700" title="Изтрий">
                  <div class="w-8 h-8 inline-flex items-center justify-center rounded-button hover:bg-red-500/10">
                    <i class="ri-delete-bin-line"></i>
                  </div>
                </a>
              </td>
            </tr>
            {% endfor %}
          {% else %}
            <tr>
              <td colspan="7" class="px-6 py-12 text-center text-sm text-gray-500">
                <div class="flex flex-col items-center">
                    <i class="ri-inbox-2-line ri-4x text-gray-300 mb-4"></i>
                    <p class="font-semibold">Няма намерени запитвания</p>
                    <p class="text-xs">Опитайте да промените филтрите или добавете нови запитвания.</p>
                </div>
              </td>
            </tr>
          {% endif %}
        </tbody>
      </table>
    </div>
    </form>

    {% if pagination_html %}
    <div class="mt-6 flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
        <div class="mb-4 md:mb-0">
            {{ pagination_html.results }}
        </div>
        <div class="mb-4 md:mb-0">
            {{ pagination_html.links }}
        </div>
        <div>
            {{ pagination_html.limit_options }}
        </div>
    </div>
    {% endif %}
  </main>

  <!-- Filter Modal -->
  <div id="filter-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden items-center justify-center z-50 p-4 md:p-0">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl transform transition-all">
      <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Разширени филтри</h3>
        <button type="button" class="text-gray-400 hover:text-gray-600 close-filter-modal">
          <i class="ri-close-line ri-lg"></i>
        </button>
      </div>
      <div class="p-6 space-y-4">
        <form id="form-filter-modal" method="get" action="{{ filter_url }}">
            {# These hidden fields will carry over the main filter values if they are set #}
            {# <input type="hidden" name="filter_status" value="{{ filter_status }}" /> #}
            {# <input type="hidden" name="filter_date_start" value="{{ filter_date_start }}" /> #}
            {# <input type="hidden" name="filter_date_end" value="{{ filter_date_end }}" /> #}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="modal-filter-name" class="block text-sm font-medium text-gray-700 mb-1">Име на клиент</label>
                    <input type="text" id="modal-filter-name" name="filter_name" value="{{ filter_name }}" placeholder="Въведете име..." class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
                <div>
                    <label for="modal-filter-email" class="block text-sm font-medium text-gray-700 mb-1">Имейл</label>
                    <input type="text" id="modal-filter-email" name="filter_email" value="{{ filter_email }}" placeholder="Въведете имейл..." class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
            </div>
            <div>
                <label for="modal-filter-subject" class="block text-sm font-medium text-gray-700 mb-1">Тема</label>
                <input type="text" id="modal-filter-subject" name="filter_subject" value="{{ filter_subject }}" placeholder="Въведете тема..." class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
            </div>
             <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="modal-filter-status-adv" class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
                    <select id="modal-filter-status-adv" name="filter_status" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                        {% for option in status_options %}
                            <option value="{{ option.value }}" {% if option.value == filter_status %}selected{% endif %}>{{ option.text }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                 <div>
                    <label for="modal-filter-date-start-adv" class="block text-sm font-medium text-gray-700 mb-1">От дата</label>
                    <input type="text" id="modal-filter-date-start-adv" name="filter_date_start" value="{{ filter_date_start }}" placeholder="дд.мм.гггг" class="date-picker bg-white border border-gray-300 rounded-button py-2 px-3 w-full focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
                <div>
                    <label for="modal-filter-date-end-adv" class="block text-sm font-medium text-gray-700 mb-1">До дата</label>
                    <input type="text" id="modal-filter-date-end-adv" name="filter_date_end" value="{{ filter_date_end }}" placeholder="дд.мм.гггг" class="date-picker bg-white border border-gray-300 rounded-button py-2 px-3 w-full focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
            </div>
        </form>
      </div>
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
        <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-button hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary close-filter-modal">Отказ</button>
        <button type="button" onclick="$('#modal-filter-name').val(''); $('#modal-filter-email').val(''); $('#modal-filter-subject').val(''); $('#modal-filter-status-adv').val(''); $('#modal-filter-date-start-adv').val(''); $('#modal-filter-date-end-adv').val(''); $('#form-filter-modal').submit();" class="px-4 py-2 text-sm font-medium text-primary bg-primary/10 border border-transparent rounded-button hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">Изчисти филтри</button>
        <button type="button" onclick="$('#form-filter-modal').submit();" class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-button hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">Приложи</button>
      </div>
    </div>
  </div>

</div> <!-- End of #content -->
<script>
$(document).ready(function() {
    // Datepicker initialization
    if (typeof $.fn.datepicker === 'function') { // Check if datepicker is loaded
        $('.date-picker').datepicker({
            format: 'dd.mm.yyyy',
            autoclose: true,
            language: 'bg', // Make sure you have the Bulgarian locale for bootstrap-datepicker
            todayHighlight: true,
            clearBtn: true
        });
    } else {
        console.error('Bootstrap Datepicker is not loaded.');
    }

    // Mobile menu toggle
    $('#mobile-menu').on('click', function() {
        // This depends on how your main sidebar (#column-left or a similar ID from OpenCart structure) is handled.
        // Typically, OpenCart's {{ column_left }} has its own mechanism or is part of a global script.
        // For a generic Tailwind sidebar, you might toggle a class like 'hidden' or 'translate-x-0'.
        // Since {{ column_left }} is standard OpenCart, it should handle its own visibility.
        // If you have a custom sidebar with id 'sidebar' as in the original HTML, you would do:
        // $('#sidebar').toggleClass('-translate-x-full'); 
        console.log('Mobile menu clicked. Sidebar visibility should be handled by OpenCart or global scripts for #column-left.');
    });

    // Filter Modal close buttons
    $('.close-filter-modal').on('click', function() {
        $('#filter-modal').addClass('hidden').removeClass('flex');
        document.body.style.overflow = 'auto';
    });

    // Handle clicking outside the modal to close it
    $('#filter-modal').on('click', function(event) {
        if ($(event.target).is($('#filter-modal'))) {
            $(this).addClass('hidden').removeClass('flex');
            document.body.style.overflow = 'auto';
        }
    });
    
    // When submitting the modal form, copy its values to the main filter form (form-filter)
    // and then submit the main filter form to ensure all parameters are sent via GET.
    $('#form-filter-modal').on('submit', function(e) {
        e.preventDefault(); // Prevent the modal form itself from submitting directly

        // Copy values from modal to main form
        $('#filter-status').val($('#modal-filter-status-adv').val());
        $('#filter-date-start').val($('#modal-filter-date-start-adv').val());
        $('#filter-date-end').val($('#modal-filter-date-end-adv').val());
        
        // Ensure filter_name, filter_email, filter_subject are part of the main form if not already
        // If they are not, we need to add them as hidden inputs or ensure they are included in the GET request.
        // For simplicity, we assume they are already part of the main form or will be added to its action URL.
        // A cleaner way is to construct the URL with all params and redirect or submit form-filter.
        
        var baseUrl = "{{ filter_url }}";
        var params = {
            filter_name: $('#modal-filter-name').val(),
            filter_email: $('#modal-filter-email').val(),
            filter_subject: $('#modal-filter-subject').val(),
            filter_status: $('#modal-filter-status-adv').val(),
            filter_date_start: $('#modal-filter-date-start-adv').val(),
            filter_date_end: $('#modal-filter-date-end-adv').val(),
            limit: "{{ limit }}" // Preserve limit if set
        };
        
        var queryString = $.param(params);
        window.location.href = baseUrl + (baseUrl.indexOf('?') > -1 ? '&' : '?') + queryString;
    });

});
</script>
{{ footer }}
