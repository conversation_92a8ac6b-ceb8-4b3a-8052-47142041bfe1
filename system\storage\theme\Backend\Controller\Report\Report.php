<?php

namespace Theme25\Backend\Controller\Report;

class Report extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'report/report');
    }

    public function index() {
        $this->setTitle('Отчети');

        // Инициализиране на данните
        $this->initAdminData();

        // Подготовка на данните с верижно извикване на методи
        // $this->prepareRequestListData()
        //      ->prepareFilterOptions()
        //      ->prepareRequestItems()
        //      ->preparePagination();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('report/report');
    }

   
}
