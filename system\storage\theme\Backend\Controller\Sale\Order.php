<?php

namespace Theme25\Backend\Controller\Sale;

class Order extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'sale/order');
    }

    public function index() {
        $this->setTitle('Поръчки');

        // Инициализиране на данните
        $this->initAdminData();

        // Подготовка на данните с верижно извикване на методи
        // $this->prepareRequestListData()
        //      ->prepareFilterOptions()
        //      ->prepareRequestItems()
        //      ->preparePagination();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('sale/order');
    }

   
}
