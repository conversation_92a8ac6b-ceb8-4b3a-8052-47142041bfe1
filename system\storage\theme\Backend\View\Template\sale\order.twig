<!-- Order Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Поръчки</h1>
                <p class="text-gray-500 mt-1">Управление на всички поръчки в магазина</p>
            </div>
            <button class="mt-4 md:mt-0 px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-add-line"></i>
                </div>
                <span>Нова поръчка</span>
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex flex-wrap items-center gap-4">
            <button id="filter-btn" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-filter-3-line"></i>
                </div>
                <span>Филтър</span>
            </button>
            <div class="w-full md:w-auto">
                <div class="relative">
                    <button id="status-dropdown-btn" class="w-full md:w-48 flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
                        <span>Всички статуси</span>
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-arrow-down-s-line"></i>
                        </div>
                    </button>
                    <div id="status-dropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded shadow-lg">
                        <ul class="py-1">
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Всички статуси</li>
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Нова</li>
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">В процес</li>
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Изпълнена</li>
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Отказана</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="w-full md:w-auto">
                <div class="relative">
                    <button id="period-dropdown-btn" class="w-full md:w-48 flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
                        <span>Период</span>
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-arrow-down-s-line"></i>
                        </div>
                    </button>
                    <div id="period-dropdown" class="hidden absolute z-10 w-64 mt-1 bg-white border border-gray-200 rounded shadow-lg p-4">
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">От дата</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">До дата</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                            </div>
                            <div class="flex justify-end">
                                <button class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Приложи</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full md:w-auto">
                <div class="relative">
                    <button id="sort-dropdown-btn" class="w-full md:w-48 flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
                        <span>Сортиране</span>
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-arrow-down-s-line"></i>
                        </div>
                    </button>
                    <div id="sort-dropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded shadow-lg">
                        <ul class="py-1">
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Последно добавени</li>
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Най-стари</li>
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Сума (възх.)</li>
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Сума (низх.)</li>
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">Клиент (А-Я)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Filter Modal -->
            <div id="filter-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
                <div class="bg-white rounded-lg w-full max-w-md mx-4">
                    <div class="flex justify-between items-center p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">Филтър</h3>
                        <button id="close-filter" class="text-gray-400 hover:text-gray-500">
                            <div class="w-6 h-6 flex items-center justify-center">
                                <i class="ri-close-line"></i>
                            </div>
                        </button>
                    </div>
                    <div class="p-6">
                        <form id="filter-form" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
                                    <option value="">Всички</option>
                                    <option value="new">Нова</option>
                                    <option value="processing">В процес</option>
                                    <option value="completed">Изпълнена</option>
                                    <option value="cancelled">Отказана</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Клиент</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Име или имейл на клиент">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Начин на плащане</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
                                    <option value="">Всички</option>
                                    <option value="card">Карта</option>
                                    <option value="bank">Банков превод</option>
                                    <option value="cash">Наложен платеж</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Сума</label>
                                <div class="flex space-x-2">
                                    <input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="От">
                                    <input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="До">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Период</label>
                                <div class="flex space-x-2">
                                    <input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                                    <input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                                </div>
                            </div>
                            <div class="flex justify-end space-x-2 mt-6">
                                <button type="button" id="reset-filter" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap">Изчисти</button>
                                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Приложи филтър</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <main class="flex-1 overflow-y-auto bg-gray-50 p-6">
        <!-- Orders Table -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    Номер
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Дата
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Клиент
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Начин на плащане
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Статус
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Сума
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Order 1 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    <span class="text-sm font-medium text-gray-900">#ORD-2025-1001</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">23.04.2025</div>
                                <div class="text-sm text-gray-500">14:32</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">Георги Иванов</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center text-sm text-gray-900">
                                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                                        <i class="ri-bank-card-line"></i>
                                    </div>
                                    Карта
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge status-new">Нова</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                1,599.00 лв.
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-eye-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-edit-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-mail-send-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-more-2-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- Order 2 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    <span class="text-sm font-medium text-gray-900">#ORD-2025-1000</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">22.04.2025</div>
                                <div class="text-sm text-gray-500">09:15</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">Мария Петрова</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center text-sm text-gray-900">
                                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                                        <i class="ri-cash-line"></i>
                                    </div>
                                    Наложен платеж
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge status-processing">В процес</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                348.50 лв.
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-eye-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-edit-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-mail-send-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-more-2-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- Order 3 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    <span class="text-sm font-medium text-gray-900">#ORD-2025-0999</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">21.04.2025</div>
                                <div class="text-sm text-gray-500">18:47</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">Димитър Стоянов</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center text-sm text-gray-900">
                                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                                        <i class="ri-bank-line"></i>
                                    </div>
                                    Банков превод
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge status-completed">Изпълнена</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                2,145.80 лв.
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-eye-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-edit-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-mail-send-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-more-2-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- Order 4 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    <span class="text-sm font-medium text-gray-900">#ORD-2025-0998</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">20.04.2025</div>
                                <div class="text-sm text-gray-500">11:23</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">Виктория Николова</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center text-sm text-gray-900">
                                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                                        <i class="ri-bank-card-line"></i>
                                    </div>
                                    Карта
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge status-cancelled">Отказана</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                799.00 лв.
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-eye-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-edit-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-mail-send-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-more-2-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- Order 5 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    <span class="text-sm font-medium text-gray-900">#ORD-2025-0997</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">19.04.2025</div>
                                <div class="text-sm text-gray-500">16:05</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">Александър Тодоров</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center text-sm text-gray-900">
                                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                                        <i class="ri-cash-line"></i>
                                    </div>
                                    Наложен платеж
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge status-processing">В процес</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                1,248.90 лв.
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-eye-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-edit-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-mail-send-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-more-2-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- Order 6 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    <span class="text-sm font-medium text-gray-900">#ORD-2025-0996</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">18.04.2025</div>
                                <div class="text-sm text-gray-500">10:38</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">Елена Димитрова</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center text-sm text-gray-900">
                                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                                        <i class="ri-bank-card-line"></i>
                                    </div>
                                    Карта
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge status-completed">Изпълнена</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                649.00 лв.
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-eye-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-edit-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-mail-send-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-more-2-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- Order 7 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    <span class="text-sm font-medium text-gray-900">#ORD-2025-0995</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">17.04.2025</div>
                                <div class="text-sm text-gray-500">14:22</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">Борислав Ангелов</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center text-sm text-gray-900">
                                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                                        <i class="ri-bank-line"></i>
                                    </div>
                                    Банков превод
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge status-completed">Изпълнена</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                3,499.00 лв.
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-eye-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-edit-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-mail-send-line"></i>
                                        </div>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-more-2-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    Показани 1-7 от 124 поръчки
                </div>
                <div class="flex items-center space-x-2">
                    <div class="relative">
                        <button id="per-page-dropdown-btn" class="flex items-center justify-between px-3 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm">
                            <span>20 на страница</span>
                            <div class="w-5 h-5 flex items-center justify-center ml-2">
                                <i class="ri-arrow-down-s-line"></i>
                            </div>
                        </button>
                        <div id="per-page-dropdown" class="hidden absolute right-0 z-10 w-40 mt-1 bg-white border border-gray-200 rounded shadow-lg">
                            <ul class="py-1">
                                <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">10 на страница</li>
                                <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">20 на страница</li>
                                <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">50 на страница</li>
                                <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer">100 на страница</li>
                            </ul>
                        </div>
                    </div>
                    <div class="flex">
                        <button class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-button text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-arrow-left-s-line"></i>
                            </div>
                        </button>
                        <button class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 bg-primary text-white">1</button>
                        <button class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 text-gray-600 hover:bg-gray-100">2</button>
                        <button class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 text-gray-600 hover:bg-gray-100">3</button>
                        <button class="w-10 h-10 flex items-center justify-center border-t border-b border-gray-300 text-gray-600 hover:bg-gray-100">4</button>
                        <button class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-button text-gray-600 hover:bg-gray-100">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-arrow-right-s-line"></i>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Order Detail Modal -->
<div id="order-detail-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">Детайли на поръчка #ORD-2025-1001</h3>
            <button id="close-order-detail" class="text-gray-400 hover:text-gray-500">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-close-line"></i>
                </div>
            </button>
        </div>
        <div class="p-6">
            <div class="flex flex-col md:flex-row justify-between mb-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 mb-1">Информация за поръчката</h4>
                    <p class="text-sm"><span class="font-medium">Дата:</span> 23.04.2025 14:32</p>
                    <p class="text-sm"><span class="font-medium">Статус:</span> <span class="text-blue-600">Нова</span></p>
                    <p class="text-sm"><span class="font-medium">Плащане:</span> Карта</p>
                    <p class="text-sm"><span class="font-medium">Обща сума:</span> 1,599.00 лв.</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <h4 class="text-sm font-medium text-gray-500 mb-1">Информация за клиента</h4>
                    <p class="text-sm"><span class="font-medium">Име:</span> Георги Иванов</p>
                    <p class="text-sm"><span class="font-medium">Имейл:</span> <EMAIL></p>
                    <p class="text-sm"><span class="font-medium">Телефон:</span> +359 888 123 456</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <h4 class="text-sm font-medium text-gray-500 mb-1">Адрес за доставка</h4>
                    <p class="text-sm">ул. Иван Вазов 25, ап. 7</p>
                    <p class="text-sm">София, 1000</p>
                    <p class="text-sm">България</p>
                </div>
            </div>
            
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h4 class="font-medium text-gray-800 mb-4">Продукти в поръчката</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Продукт
                                </th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Количество
                                </th>
                                <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Ед. цена
                                </th>
                                <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Сума
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded">
                                            <img class="h-10 w-10 rounded object-cover object-top" src="https://readdy.ai/api/search-image?query=professional%2520photo%2520of%2520modern%2520smartphone%2520with%2520sleek%2520design%2520on%2520clean%2520white%2520background%252C%2520high%2520resolution%2520product%2520photography%252C%2520minimalist%2520style&width=100&height=100&seq=1&orientation=squarish" alt="">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">Смартфон Samsung Galaxy S22</div>
                                            <div class="text-sm text-gray-500">Код: PRD-1001</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 text-center text-sm text-gray-900">
                                    1
                                </td>
                                <td class="px-4 py-4 text-right text-sm text-gray-900">
                                    1,599.00 лв.
                                </td>
                                <td class="px-4 py-4 text-right text-sm font-medium text-gray-900">
                                    1,599.00 лв.
                                </td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr class="bg-gray-50">
                                <td colspan="2" class="px-4 py-3"></td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-700">Междинна сума:</td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-900">1,599.00 лв.</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td colspan="2" class="px-4 py-3"></td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-700">Доставка:</td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-900">0.00 лв.</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td colspan="2" class="px-4 py-3"></td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-gray-700">Обща сума:</td>
                                <td class="px-4 py-3 text-right text-sm font-medium text-primary">1,599.00 лв.</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h4 class="font-medium text-gray-800 mb-4">Промяна на статус</h4>
                <div class="flex flex-wrap items-center gap-4">
                    <button class="px-4 py-2 bg-blue-100 text-blue-700 rounded-button hover:bg-blue-200 text-sm whitespace-nowrap">Нова</button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 text-sm whitespace-nowrap">В процес</button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 text-sm whitespace-nowrap">Изпълнена</button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 text-sm whitespace-nowrap">Отказана</button>
                </div>
            </div>
            
            <div class="border-t border-gray-200 pt-6">
                <h4 class="font-medium text-gray-800 mb-4">Изпращане на известие</h4>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Тема</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" value="Информация за поръчка #ORD-2025-1001">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Съобщение</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm h-32" placeholder="Въведете текст на съобщението...">Здравейте, Георги Иванов!

Благодарим ви за вашата поръчка #ORD-2025-1001. 

С уважение,
Екипът на Rakla</textarea>
                </div>
                <div class="flex justify-end">
                    <button class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Изпрати известие</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter modal functionality
    const filterBtn = document.getElementById('filter-btn');
    const filterModal = document.getElementById('filter-modal');
    const closeFilter = document.getElementById('close-filter');
    const filterForm = document.getElementById('filter-form');
    const resetFilter = document.getElementById('reset-filter');
    
    filterBtn.addEventListener('click', function() {
        filterModal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    });
    
    closeFilter.addEventListener('click', function() {
        filterModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    });
    
    filterModal.addEventListener('click', function(e) {
        if (e.target === filterModal) {
            filterModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    });
    
    filterForm.addEventListener('submit', function(e) {
        e.preventDefault();
        filterModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    });
    
    resetFilter.addEventListener('click', function() {
        filterForm.reset();
    });
    
    // Toggle sidebar
    const toggleSidebar = document.getElementById('toggle-sidebar');
    const sidebar = document.getElementById('sidebar');
    const mobileMenu = document.getElementById('mobile-menu');
    
    toggleSidebar.addEventListener('click', function() {
        sidebar.classList.toggle('w-64');
        sidebar.classList.toggle('w-20');
        const sidebarItems = document.querySelectorAll('.sidebar-item span');
        const sidebarLogo = document.getElementById('sidebar-logo');
        sidebarItems.forEach(item => {
            item.classList.toggle('hidden');
        });
        sidebarLogo.classList.toggle('hidden');
        const icon = toggleSidebar.querySelector('i');
        if (icon.classList.contains('ri-menu-fold-line')) {
            icon.classList.remove('ri-menu-fold-line');
            icon.classList.add('ri-menu-unfold-line');
        } else {
            icon.classList.remove('ri-menu-unfold-line');
            icon.classList.add('ri-menu-fold-line');
        }
    });
    
    mobileMenu.addEventListener('click', function() {
        sidebar.classList.toggle('-translate-x-full');
    });
    
    // Status dropdown
    const statusDropdownBtn = document.getElementById('status-dropdown-btn');
    const statusDropdown = document.getElementById('status-dropdown');
    
    statusDropdownBtn.addEventListener('click', function() {
        statusDropdown.classList.toggle('hidden');
    });
    
    // Period dropdown
    const periodDropdownBtn = document.getElementById('period-dropdown-btn');
    const periodDropdown = document.getElementById('period-dropdown');
    
    periodDropdownBtn.addEventListener('click', function() {
        periodDropdown.classList.toggle('hidden');
    });
    
    // Sort dropdown
    const sortDropdownBtn = document.getElementById('sort-dropdown-btn');
    const sortDropdown = document.getElementById('sort-dropdown');
    
    sortDropdownBtn.addEventListener('click', function() {
        sortDropdown.classList.toggle('hidden');
    });
    
    // Per page dropdown
    const perPageDropdownBtn = document.getElementById('per-page-dropdown-btn');
    const perPageDropdown = document.getElementById('per-page-dropdown');
    
    perPageDropdownBtn.addEventListener('click', function() {
        perPageDropdown.classList.toggle('hidden');
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!statusDropdownBtn.contains(event.target) && !statusDropdown.contains(event.target)) {
            statusDropdown.classList.add('hidden');
        }
        if (!periodDropdownBtn.contains(event.target) && !periodDropdown.contains(event.target)) {
            periodDropdown.classList.add('hidden');
        }
        if (!sortDropdownBtn.contains(event.target) && !sortDropdown.contains(event.target)) {
            sortDropdown.classList.add('hidden');
        }
        if (!perPageDropdownBtn.contains(event.target) && !perPageDropdown.contains(event.target)) {
            perPageDropdown.classList.add('hidden');
        }
    });
    
    // Select status option
    const statusItems = statusDropdown.querySelectorAll('li');
    statusItems.forEach(item => {
        item.addEventListener('click', function() {
            statusDropdownBtn.querySelector('span').textContent = this.textContent;
            statusDropdown.classList.add('hidden');
        });
    });
    
    // Select sort option
    const sortItems = sortDropdown.querySelectorAll('li');
    sortItems.forEach(item => {
        item.addEventListener('click', function() {
            sortDropdownBtn.querySelector('span').textContent = this.textContent;
            sortDropdown.classList.add('hidden');
        });
    });
    
    // Select per page option
    const perPageItems = perPageDropdown.querySelectorAll('li');
    perPageItems.forEach(item => {
        item.addEventListener('click', function() {
            perPageDropdownBtn.querySelector('span').textContent = this.textContent;
            perPageDropdown.classList.add('hidden');
        });
    });
    
    // Order detail modal
    const viewOrderBtns = document.querySelectorAll('.ri-eye-line');
    const orderDetailModal = document.getElementById('order-detail-modal');
    const closeOrderDetail = document.getElementById('close-order-detail');
    
    viewOrderBtns.forEach(btn => {
        btn.parentElement.parentElement.addEventListener('click', function() {
            orderDetailModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        });
    });
    
    closeOrderDetail.addEventListener('click', function() {
        orderDetailModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    });
    
    orderDetailModal.addEventListener('click', function(e) {
        if (e.target === orderDetailModal) {
            orderDetailModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    });
});
</script>