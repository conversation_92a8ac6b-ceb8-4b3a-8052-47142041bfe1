<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div class="flex items-center">
            <a href="{{ back_url }}" data-readdy="true" class="mr-3 text-gray-500 hover:text-primary">
                <div class="w-8 h-8 flex items-center justify-center">
                    <i class="ri-arrow-left-line ri-lg"></i>
                </div>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-800">
                    {% if product_id %}Редакция на продукт{% else %}Добавяне на продукт{% endif %}
                </h1>
            </div>
        </div>
        <div class="flex items-center space-x-3 mt-4 md:mt-0">
            <button type="button" onclick="location.href='{{ back_url }}'" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors whitespace-nowrap !rounded-button">
                <span>Отказ</span>
            </button>
            <button type="submit" form="form-product" id="save-product" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-save-line"></i>
                </div>
                <span>Запази</span>
            </button>
        </div>
    </div>
</div>
<div class="p-6">
    <form action="" method="post" enctype="multipart/form-data" id="form-product" class="space-y-6">
        <ul class="nav nav-tabs flex flex-wrap border-b border-gray-200" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:border-primary focus:outline-none" id="general-tab" data-toggle="tab" data-target="#tab-general" type="button" role="tab" aria-controls="tab-general" aria-selected="true">Общи</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:border-primary focus:outline-none" id="data-tab" data-toggle="tab" data-target="#tab-data" type="button" role="tab" aria-controls="tab-data" aria-selected="false">Данни</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:border-primary focus:outline-none" id="links-tab" data-toggle="tab" data-target="#tab-links" type="button" role="tab" aria-controls="tab-links" aria-selected="false">Връзки</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:border-primary focus:outline-none" id="attribute-tab" data-toggle="tab" data-target="#tab-attribute" type="button" role="tab" aria-controls="tab-attribute" aria-selected="false">Атрибути</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:border-primary focus:outline-none" id="option-tab" data-toggle="tab" data-target="#tab-option" type="button" role="tab" aria-controls="tab-option" aria-selected="false">Опции</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:border-primary focus:outline-none" id="special-tab" data-toggle="tab" data-target="#tab-special" type="button" role="tab" aria-controls="tab-special" aria-selected="false">Специални</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:border-primary focus:outline-none" id="image-tab" data-toggle="tab" data-target="#tab-image" type="button" role="tab" aria-controls="tab-image" aria-selected="false">Изображения</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:border-primary focus:outline-none" id="reward-tab" data-toggle="tab" data-target="#tab-reward" type="button" role="tab" aria-controls="tab-reward" aria-selected="false">Точки за награди</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:border-primary focus:outline-none" id="seo-tab" data-toggle="tab" data-target="#tab-seo" type="button" role="tab" aria-controls="tab-seo" aria-selected="false">SEO</button>
            </li>
        </ul>

        <div class="tab-content py-4">
            <div class="tab-pane fade show active" id="tab-general" role="tabpanel" aria-labelledby="general-tab">
                {% for language in languages %}
                    <div class="bg-gray-50 p-4 rounded-md mb-4 border border-gray-200">
                        <div class="flex items-center mb-4">
                            <img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" class="w-6 h-6 mr-3">
                            <h3 class="text-lg font-semibold text-gray-800">{{ language.name }}</h3>
                        </div>

                        <div class="mb-4">
                            <label for="input-name-{{ language.language_id }}" class="block text-sm font-medium text-gray-700 mb-1">Име на продукт</label>
                            <input type="text" name="product_description[{{ language.language_id }}][name]" value="{{ product_description[language.language_id] ? product_description[language.language_id].name : '' }}" placeholder="Име на продукт" id="input-name-{{ language.language_id }}" class="form-input">
                        </div>

                        <div class="mb-4">
                            <label for="input-description-{{ language.language_id }}" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
                            <textarea name="product_description[{{ language.language_id }}][description]" placeholder="Описание" id="input-description-{{ language.language_id }}" class="form-textarea">{{ product_description[language.language_id] ? product_description[language.language_id].description : '' }}</textarea>
                        </div>

                        <div class="mb-4">
                            <label for="input-meta-title-{{ language.language_id }}" class="block text-sm font-medium text-gray-700 mb-1">Мета Заглавие</label>
                            <input type="text" name="product_description[{{ language.language_id }}][meta_title]" value="{{ product_description[language.language_id] ? product_description[language.language_id].meta_title : '' }}" placeholder="Мета Заглавие" id="input-meta-title-{{ language.language_id }}" class="form-input">
                        </div>

                        <div class="mb-4">
                            <label for="input-meta-description-{{ language.language_id }}" class="block text-sm font-medium text-gray-700 mb-1">Мета Описание</label>
                            <textarea name="product_description[{{ language.language_id }}][meta_description]" placeholder="Мета Описание" id="input-meta-description-{{ language.language_id }}" class="form-textarea">{{ product_description[language.language_id] ? product_description[language.language_id].meta_description : '' }}</textarea>
                        </div>

                        <div class="mb-4">
                            <label for="input-meta-keyword-{{ language.language_id }}" class="block text-sm font-medium text-gray-700 mb-1">Мета Ключови думи</label>
                            <textarea name="product_description[{{ language.language_id }}][meta_keyword]" placeholder="Мета Ключови думи" id="input-meta-keyword-{{ language.language_id }}" class="form-textarea">{{ product_description[language.language_id] ? product_description[language.language_id].meta_keyword : '' }}</textarea>
                        </div>

                        <div class="mb-4">
                            <label for="input-tag-{{ language.language_id }}" class="block text-sm font-medium text-gray-700 mb-1">Етикети на продукт</label>
                            <input type="text" name="product_description[{{ language.language_id }}][tag]" value="{{ product_description[language.language_id] ? product_description[language.language_id].tag : '' }}" placeholder="Етикети на продукт" id="input-tag-{{ language.language_id }}" class="form-input">
                            <p class="text-xs text-gray-500 mt-1">Отделни етикети със запетая (,)</p>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <div class="tab-pane fade" id="tab-data" role="tabpanel" aria-labelledby="data-tab">
                <div class="mb-4">
                    <label for="input-model" class="block text-sm font-medium text-gray-700 mb-1">Модел</label>
                    <input type="text" name="model" value="{{ model }}" placeholder="Модел" id="input-model" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-sku" class="block text-sm font-medium text-gray-700 mb-1">SKU</label>
                    <input type="text" name="sku" value="{{ sku }}" placeholder="SKU" id="input-sku" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-upc" class="block text-sm font-medium text-gray-700 mb-1">UPC</label>
                    <input type="text" name="upc" value="{{ upc }}" placeholder="UPC" id="input-upc" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-ean" class="block text-sm font-medium text-gray-700 mb-1">EAN</label>
                    <input type="text" name="ean" value="{{ ean }}" placeholder="EAN" id="input-ean" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-jan" class="block text-sm font-medium text-gray-700 mb-1">JAN</label>
                    <input type="text" name="jan" value="{{ jan }}" placeholder="JAN" id="input-jan" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-isbn" class="block text-sm font-medium text-gray-700 mb-1">ISBN</label>
                    <input type="text" name="isbn" value="{{ isbn }}" placeholder="ISBN" id="input-isbn" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-mpn" class="block text-sm font-medium text-gray-700 mb-1">MPN</label>
                    <input type="text" name="mpn" value="{{ mpn }}" placeholder="MPN" id="input-mpn" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-location" class="block text-sm font-medium text-gray-700 mb-1">Местоположение</label>
                    <input type="text" name="location" value="{{ location }}" placeholder="Местоположение" id="input-location" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-price" class="block text-sm font-medium text-gray-700 mb-1">Цена</label>
                    <input type="text" name="price" value="{{ price }}" placeholder="Цена" id="input-price" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-tax-class" class="block text-sm font-medium text-gray-700 mb-1">Данъчен клас</label>
                    <select name="tax_class_id" id="input-tax-class" class="form-select">
                        <option value="0">--- Без ---</option>
                        {% for tax_class in tax_classes %}
                            <option value="{{ tax_class.tax_class_id }}" {% if tax_class.tax_class_id == tax_class_id %}selected="selected"{% endif %}>{{ tax_class.title }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-4">
                    <label for="input-quantity" class="block text-sm font-medium text-gray-700 mb-1">Количество</label>
                    <input type="number" name="quantity" value="{{ quantity }}" placeholder="Количество" id="input-quantity" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-minimum" class="block text-sm font-medium text-gray-700 mb-1">Минимално количество</label>
                    <input type="number" name="minimum" value="{{ minimum }}" placeholder="Минимално количество" id="input-minimum" class="form-input">
                    <p class="text-xs text-gray-500 mt-1">Минимално количество за поръчка</p>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Изваждане от наличност</label>
                    <div class="flex items-center">
                        <input type="radio" name="subtract" value="1" id="subtract-yes" class="form-radio" {% if subtract %}checked{% endif %}>
                        <label for="subtract-yes" class="ml-2 mr-4">Да</label>
                        <input type="radio" name="subtract" value="0" id="subtract-no" class="form-radio" {% if not subtract %}checked{% endif %}>
                        <label for="subtract-no" class="ml-2">Не</label>
                    </div>
                </div>
                <div class="mb-4">
                    <label for="input-stock-status" class="block text-sm font-medium text-gray-700 mb-1">Статус извън наличност</label>
                    <select name="stock_status_id" id="input-stock-status" class="form-select">
                        {% for stock_status in stock_statuses %}
                            <option value="{{ stock_status.stock_status_id }}" {% if stock_status.stock_status_id == stock_status_id %}selected="selected"{% endif %}>{{ stock_status.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-4">
                    <label for="input-date-available" class="block text-sm font-medium text-gray-700 mb-1">Дата на наличност</label>
                    <input type="date" name="date_available" value="{{ date_available }}" id="input-date-available" class="form-input">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Изисква доставка</label>
                    <div class="flex items-center">
                        <input type="radio" name="shipping" value="1" id="shipping-yes" class="form-radio" {% if shipping %}checked{% endif %}>
                        <label for="shipping-yes" class="ml-2 mr-4">Да</label>
                        <input type="radio" name="shipping" value="0" id="shipping-no" class="form-radio" {% if not shipping %}checked{% endif %}>
                        <label for="shipping-no" class="ml-2">Не</label>
                    </div>
                </div>
                <div class="mb-4">
                    <label for="input-length" class="block text-sm font-medium text-gray-700 mb-1">Размери (Д x Ш x В)</label>
                    <div class="grid grid-cols-3 gap-4">
                        <input type="text" name="length" value="{{ length }}" placeholder="Дължина" id="input-length" class="form-input">
                        <input type="text" name="width" value="{{ width }}" placeholder="Ширина" id="input-width" class="form-input">
                        <input type="text" name="height" value="{{ height }}" placeholder="Височина" id="input-height" class="form-input">
                    </div>
                </div>
                <div class="mb-4">
                    <label for="input-length-class" class="block text-sm font-medium text-gray-700 mb-1">Единица за дължина</label>
                    <select name="length_class_id" id="input-length-class" class="form-select">
                        {% for length_class in length_classes %}
                            <option value="{{ length_class.length_class_id }}" {% if length_class.length_class_id == length_class_id %}selected="selected"{% endif %}>{{ length_class.title }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-4">
                    <label for="input-weight" class="block text-sm font-medium text-gray-700 mb-1">Тегло</label>
                    <input type="text" name="weight" value="{{ weight }}" placeholder="Тегло" id="input-weight" class="form-input">
                </div>
                <div class="mb-4">
                    <label for="input-weight-class" class="block text-sm font-medium text-gray-700 mb-1">Единица за тегло</label>
                    <select name="weight_class_id" id="input-weight-class" class="form-select">
                        {% for weight_class in weight_classes %}
                            <option value="{{ weight_class.weight_class_id }}" {% if weight_class.weight_class_id == weight_class_id %}selected="selected"{% endif %}>{{ weight_class.title }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-4">
                    <label for="input-status" class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
                    <select name="status" id="input-status" class="form-select">
                        <option value="1" {% if status %}selected="selected"{% endif %}>Включен</option>
                        <option value="0" {% if not status %}selected="selected"{% endif %}>Изключен</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="input-sort-order" class="block text-sm font-medium text-gray-700 mb-1">Подредба</label>
                    <input type="number" name="sort_order" value="{{ sort_order }}" placeholder="Подредба" id="input-sort-order" class="form-input">
                </div>
            </div>

            <div class="tab-pane fade" id="tab-links" role="tabpanel" aria-labelledby="links-tab">
                <div class="mb-4">
                    <label for="input-manufacturer" class="block text-sm font-medium text-gray-700 mb-1">Производител</label>
                    <select name="manufacturer_id" id="input-manufacturer" class="form-select">
                        <option value="0">--- Няма ---</option>
                        {% for manufacturer in manufacturers %}
                            <option value="{{ manufacturer.manufacturer_id }}" {% if manufacturer.manufacturer_id == manufacturer_id %}selected="selected"{% endif %}>{{ manufacturer.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Категории</label>
                    <div class="border rounded p-2 bg-gray-50 max-h-48 overflow-y-auto">
                        {% for category in categories %} {# Assuming 'categories' is passed from controller #}
                            <div class="flex items-center mb-1">
                                <input type="checkbox" name="product_category[]" value="{{ category.category_id }}" id="category-{{ category.category_id }}" class="form-checkbox" {% if category.category_id in product_category %}checked{% endif %}>
                                <label for="category-{{ category.category_id }}" class="ml-2 text-sm">{{ category.name }}</label>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Филтри</label>
                    <input type="text" name="product_filter" value="" placeholder="Филтри (Autocomplete)" id="input-filter" class="form-input">
                    <div id="product-filter" class="flex flex-wrap gap-2 mt-2">
                        {% for filter in product_filter %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ filter.name }}
                                <button type="button" class="ml-1 text-blue-800 hover:text-blue-900 remove-filter" data-filter-id="{{ filter.filter_id }}">&times;</button>
                                <input type="hidden" name="product_filter[]" value="{{ filter.filter_id }}">
                            </span>
                        {% endfor %}
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Свързани продукти</label>
                    <input type="text" name="product_related" value="" placeholder="Свързани продукти (Autocomplete)" id="input-related" class="form-input">
                    <div id="product-related" class="flex flex-wrap gap-2 mt-2">
                        {% for related in product_related %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ related.name }}
                                <button type="button" class="ml-1 text-blue-800 hover:text-blue-900 remove-related" data-product-id="{{ related.product_id }}">&times;</button>
                                <input type="hidden" name="product_related[]" value="{{ related.product_id }}">
                            </span>
                        {% endfor %}
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Изтегляния</label>
                    <input type="text" name="product_download" value="" placeholder="Изтегляния (Autocomplete)" id="input-download" class="form-input">
                    <div id="product-download" class="flex flex-wrap gap-2 mt-2">
                        {% for download in product_download %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ download.name }}
                                <button type="button" class="ml-1 text-blue-800 hover:text-blue-900 remove-download" data-download-id="{{ download.download_id }}">&times;</button>
                                <input type="hidden" name="product_download[]" value="{{ download.download_id }}">
                            </span>
                        {% endfor %}
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Магазини</label>
                    {% for store in stores %}
                        <div class="flex items-center">
                            <input type="checkbox" name="product_store[]" value="{{ store.store_id }}" id="store-{{ store.store_id }}" class="form-checkbox" {% if store.store_id in product_store %}checked{% endif %}>
                            <label for="store-{{ store.store_id }}" class="ml-2">{{ store.name }}</label>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <div class="tab-pane fade" id="tab-attribute" role="tabpanel" aria-labelledby="attribute-tab">
                <div id="product-attribute" class="space-y-4">
                    {% set attribute_row = 0 %}
                    {% for product_attribute in product_attribute %}
                        <div class="flex items-center space-x-2">
                            <select name="product_attribute[{{ attribute_row }}][attribute_id]" class="form-select flex-1">
                                <option value="">--- Изберете атрибут ---</option>
                                {% for attribute_group in attribute_groups %}
                                    <optgroup label="{{ attribute_group.name }}">
                                        {% for attribute in attribute_group.attribute %}
                                            <option value="{{ attribute.attribute_id }}" {% if attribute.attribute_id == product_attribute.attribute_id %}selected="selected"{% endif %}>{{ attribute.name }}</option>
                                        {% endfor %}
                                    </optgroup>
                                {% endfor %}
                            </select>
                            {% for language in languages %}
                                <textarea name="product_attribute[{{ attribute_row }}][product_attribute_description][{{ language.language_id }}][text]" placeholder="Текст на атрибут ({{ language.name }})" class="form-textarea flex-1">{{ product_attribute.product_attribute_description[language.language_id] ? product_attribute.product_attribute_description[language.language_id].text : '' }}</textarea>
                            {% endfor %}
                            <button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-attribute">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                        {% set attribute_row = attribute_row + 1 %}
                    {% endfor %}
                </div>
                <button type="button" id="button-attribute-add" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    Добави атрибут
                </button>
            </div>

            <div class="tab-pane fade" id="tab-option" role="tabpanel" aria-labelledby="option-tab">
                <div id="product-option" class="space-y-4">
                    {% set option_row = 0 %}
                    {% for product_option in product_option %}
                        <div class="flex items-center space-x-2">
                            <select name="product_option[{{ option_row }}][option_id]" class="form-select flex-1">
                                <option value="">--- Изберете опция ---</option>
                                {% for option in options %}
                                    <option value="{{ option.option_id }}" {% if option.option_id == product_option.option_id %}selected="selected"{% endif %}>{{ option.name }}</option>
                                {% endfor %}
                            </select>
                            <input type="checkbox" name="product_option[{{ option_row }}][required]" value="1" {% if product_option.required %}checked{% endif %}>
                            <label>Задължително</label>
                            <button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-option">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                        <div class="ml-8 space-y-2 product-option-values-{{ option_row }}">
                            {% set option_value_row = 0 %}
                            {% for product_option_value in product_option.product_option_value %}
                                <div class="flex items-center space-x-2">
                                    <select name="product_option[{{ option_row }}][product_option_value][{{ option_value_row }}][option_value_id]" class="form-select flex-1">
                                        <option value="">--- Изберете стойност ---</option>
                                        {% for option_value in product_option.option_values_all %} {# Assuming you'd pass all possible option values for this option_id #}
                                            <option value="{{ option_value.option_value_id }}" {% if option_value.option_value_id == product_option_value.option_value_id %}selected="selected"{% endif %}>{{ option_value.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <input type="text" name="product_option[{{ option_row }}][product_option_value][{{ option_value_row }}][quantity]" value="{{ product_option_value.quantity }}" placeholder="Количество" class="form-input w-24">
                                    <input type="text" name="product_option[{{ option_row }}][product_option_value][{{ option_value_row }}][price]" value="{{ product_option_value.price }}" placeholder="Цена" class="form-input w-24">
                                    <select name="product_option[{{ option_row }}][product_option_value][{{ option_value_row }}][price_prefix]" class="form-select w-16">
                                        <option value="+" {% if product_option_value.price_prefix == '+' %}selected="selected"{% endif %}>+</option>
                                        <option value="-" {% if product_option_value.price_prefix == '-' %}selected="selected"{% endif %}>-</option>
                                    </select>
                                    <input type="text" name="product_option[{{ option_row }}][product_option_value][{{ option_value_row }}][points]" value="{{ product_option_value.points }}" placeholder="Точки" class="form-input w-24">
                                    <select name="product_option[{{ option_row }}][product_option_value][{{ option_value_row }}][points_prefix]" class="form-select w-16">
                                        <option value="+" {% if product_option_value.points_prefix == '+' %}selected="selected"{% endif %}>+</option>
                                        <option value="-" {% if product_option_value.points_prefix == '-' %}selected="selected"{% endif %}>-</option>
                                    </select>
                                    <input type="text" name="product_option[{{ option_row }}][product_option_value][{{ option_value_row }}][weight]" value="{{ product_option_value.weight }}" placeholder="Тегло" class="form-input w-24">
                                    <select name="product_option[{{ option_row }}][product_option_value][{{ option_value_row }}][weight_prefix]" class="form-select w-16">
                                        <option value="+" {% if product_option_value.weight_prefix == '+' %}selected="selected"{% endif %}>+</option>
                                        <option value="-" {% if product_option_value.weight_prefix == '-' %}selected="selected"{% endif %}>-</option>
                                    </select>
                                    <button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-option-value">
                                        <i class="ri-delete-bin-line"></i>
                                    </button>
                                </div>
                                {% set option_value_row = option_value_row + 1 %}
                            {% endfor %}
                            <button type="button" class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors add-option-value" data-option-row="{{ option_row }}" data-option-value-row-start="{{ option_value_row }}">
                                Добави стойност на опция
                            </button>
                        </div>
                        {% set option_row = option_row + 1 %}
                    {% endfor %}
                </div>
                <button type="button" id="button-option-add" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    Добави опция
                </button>
            </div>

            <div class="tab-pane fade" id="tab-special" role="tabpanel" aria-labelledby="special-tab">
                <div id="product-special" class="space-y-4">
                    {% set special_row = 0 %}
                    {% for product_special in product_special %}
                        <div class="flex items-center space-x-2">
                            <select name="product_special[{{ special_row }}][customer_group_id]" class="form-select flex-1">
                                {% for customer_group in customer_groups %}
                                    <option value="{{ customer_group.customer_group_id }}" {% if customer_group.customer_group_id == product_special.customer_group_id %}selected="selected"{% endif %}>{{ customer_group.name }}</option>
                                {% endfor %}
                            </select>
                            <input type="text" name="product_special[{{ special_row }}][priority]" value="{{ product_special.priority }}" placeholder="Приоритет" class="form-input w-24">
                            <input type="text" name="product_special[{{ special_row }}][price]" value="{{ product_special.price }}" placeholder="Цена" class="form-input w-24">
                            <input type="date" name="product_special[{{ special_row }}][date_start]" value="{{ product_special.date_start }}" placeholder="Начална дата" class="form-input w-32">
                            <input type="date" name="product_special[{{ special_row }}][date_end]" value="{{ product_special.date_end }}" placeholder="Крайна дата" class="form-input w-32">
                            <button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-special">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                        {% set special_row = special_row + 1 %}
                    {% endfor %}
                </div>
                <button type="button" id="button-special-add" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    Добави специална цена
                </button>
            </div>

            <div class="tab-pane fade" id="tab-image" role="tabpanel" aria-labelledby="image-tab">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Основно изображение</label>
                    <a href="" id="thumb-image" data-toggle="image" class="img-thumbnail float-left">
                        <img src="{{ image ? image : placeholder_image }}" alt="" title="" data-placeholder="{{ placeholder_image }}" />
                    </a>
                    <input type="hidden" name="image" value="{{ image }}" id="input-image" />
                </div>
                <div id="images" class="space-y-4">
                    {% set image_row = 0 %}
                    {% for product_image in product_image %}
                        <div class="flex items-center space-x-2">
                            <a href="" id="thumb-image{{ image_row }}" data-toggle="image" class="img-thumbnail">
                                <img src="{{ product_image.thumb }}" alt="" title="" data-placeholder="{{ placeholder_image }}" />
                            </a>
                            <input type="hidden" name="product_image[{{ image_row }}][image]" value="{{ product_image.image }}" id="input-image{{ image_row }}" />
                            <input type="text" name="product_image[{{ image_row }}][sort_order]" value="{{ product_image.sort_order }}" placeholder="Подредба" class="form-input w-24">
                            <button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-image">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                        {% set image_row = image_row + 1 %}
                    {% endfor %}
                </div>
                <button type="button" id="button-image-add" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    Добави изображение
                </button>
            </div>

            <div class="tab-pane fade" id="tab-reward" role="tabpanel" aria-labelledby="reward-tab">
                <div id="product-reward" class="space-y-4">
                    {% set reward_row = 0 %}
                    {% for product_reward in product_reward %}
                        <div class="flex items-center space-x-2">
                            <select name="product_reward[{{ reward_row }}][customer_group_id]" class="form-select flex-1">
                                {% for customer_group in customer_groups %}
                                    <option value="{{ customer_group.customer_group_id }}" {% if customer_group.customer_group_id == product_reward.customer_group_id %}selected="selected"{% endif %}>{{ customer_group.name }}</option>
                                {% endfor %}
                            </select>
                            <input type="text" name="product_reward[{{ reward_row }}][points]" value="{{ product_reward.points }}" placeholder="Точки" class="form-input w-24">
                            <button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-reward">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                        {% set reward_row = reward_row + 1 %}
                    {% endfor %}
                </div>
                <button type="button" id="button-reward-add" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    Добави точка за награда
                </button>
            </div>

            <div class="tab-pane fade" id="tab-seo" role="tabpanel" aria-labelledby="seo-tab">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">SEO URL</label>
                    {% for language in languages %}
                        <div class="flex items-center mb-2">
                            <img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" class="w-5 h-5 mr-2">
                            <input type="text" name="product_seo_url[{{ language.language_id }}][keyword]" value="{{ product_seo_url[language.language_id] ? product_seo_url[language.language_id].keyword : '' }}" placeholder="SEO URL ({{ language.name }})" class="form-input flex-1">
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </form>
</div>

<script>
    // JavaScript за табовете
    document.addEventListener('DOMContentLoaded', function() {
        const tabs = document.querySelectorAll('.nav-link');
        const tabContents = document.querySelectorAll('.tab-pane');

        tabs.forEach(tab => {
            tab.addEventListener('click', function(event) {
                event.preventDefault();
                const targetId = this.dataset.target;

                // Remove active classes
                tabs.forEach(t => t.classList.remove('active', 'border-primary'));
                tabContents.forEach(tc => tc.classList.remove('show', 'active'));

                // Add active classes to clicked tab and its content
                this.classList.add('active', 'border-primary');
                document.querySelector(targetId).classList.add('show', 'active');
            });
        });

        // Activate the first tab by default
        if (tabs.length > 0) {
            tabs[0].click();
        }
    });

    // Helper to get next row index
    function getNextRowIndex(elementId) {
        return $(elementId + ' > div').length;
    }

    // Add Attribute
    $('#button-attribute-add').on('click', function() {
        let attributeRow = getNextRowIndex('#product-attribute');
        let html = '<div class="flex items-center space-x-2">';
        html += '<select name="product_attribute[' + attributeRow + '][attribute_id]" class="form-select flex-1">';
        html += '<option value="">--- Изберете атрибут ---</option>';
        {% for attribute_group in attribute_groups %}
            html += '<optgroup label="{{ attribute_group.name }}">';
            {% for attribute in attribute_group.attribute %}
                html += '<option value="{{ attribute.attribute_id }}">{{ attribute.name }}</option>';
            {% endfor %}
            html += '</optgroup>';
        {% endfor %}
        html += '</select>';
        {% for language in languages %}
            html += '<textarea name="product_attribute[' + attributeRow + '][product_attribute_description][{{ language.language_id }}][text]" placeholder="Текст на атрибут ({{ language.name }})" class="form-textarea flex-1"></textarea>';
        {% endfor %}
        html += '<button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-attribute">';
        html += '<i class="ri-delete-bin-line"></i>';
        html += '</button>';
        html += '</div>';

        $('#product-attribute').append(html);
    });

    // Remove Attribute
    $(document).on('click', '.remove-attribute', function() {
        $(this).closest('.flex').remove();
    });

    // Add Option
    $('#button-option-add').on('click', function() {
        let optionRow = getNextRowIndex('#product-option');
        let html = '<div class="flex items-center space-x-2">';
        html += '<select name="product_option[' + optionRow + '][option_id]" class="form-select flex-1">';
        html += '<option value="">--- Изберете опция ---</option>';
        {% for option in options %}
            html += '<option value="{{ option.option_id }}">{{ option.name }}</option>';
        {% endfor %}
        html += '</select>';
        html += '<input type="checkbox" name="product_option[' + optionRow + '][required]" value="1">';
        html += '<label>Задължително</label>';
        html += '<button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-option">';
        html += '<i class="ri-delete-bin-line"></i>';
        html += '</button>';
        html += '</div>';
        html += '<div class="ml-8 space-y-2 product-option-values-' + optionRow + '">'; // Container for option values
        html += '<button type="button" class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors add-option-value" data-option-row="' + optionRow + '">';
        html += 'Добави стойност на опция';
        html += '</button>';
        html += '</div>';

        $('#product-option').append(html);
    });

    // Remove Option
    $(document).on('click', '.remove-option', function() {
        $(this).closest('.flex').next('div[class^="product-option-values-"]').remove(); // Remove associated values container
        $(this).closest('.flex').remove();
    });

    // Add Option Value
    $(document).on('click', '.add-option-value', function() {
        const currentOptionRow = $(this).data('option-row');
        let optionValueRow = $(this).data('option-value-row-start') || 0; // Initialize from data attribute or 0
        if (!optionValueRow) { // if not defined, get current count of existing values
            optionValueRow = $(this).closest('div[class^="product-option-values-"]').children('div').length;
        }

        let html = '<div class="flex items-center space-x-2">';
        html += '<select name="product_option[' + currentOptionRow + '][product_option_value][' + optionValueRow + '][option_value_id]" class="form-select flex-1">';
        html += '<option value="">--- Изберете стойност ---</option>';
        // This part needs AJAX to load specific option values based on the selected option_id
        // For now, it will list all available option values (not ideal for real world)
        {% for option in options %}
            {% for option_value in option.option_value %}
                html += '<option value="{{ option_value.option_value_id }}">{{ option_value.name }}</option>';
            {% endfor %}
        {% endfor %}
        html += '</select>';
        html += '<input type="text" name="product_option[' + currentOptionRow + '][product_option_value][' + optionValueRow + '][quantity]" placeholder="Количество" class="form-input w-24">';
        html += '<input type="text" name="product_option[' + currentOptionRow + '][product_option_value][' + optionValueRow + '][price]" placeholder="Цена" class="form-input w-24">';
        html += '<select name="product_option[' + currentOptionRow + '][product_option_value][' + optionValueRow + '][price_prefix]" class="form-select w-16">';
        html += '<option value="+">+</option>';
        html += '<option value="-">-</option>';
        html += '</select>';
        html += '<input type="text" name="product_option[' + currentOptionRow + '][product_option_value][' + optionValueRow + '][points]" placeholder="Точки" class="form-input w-24">';
        html += '<select name="product_option[' + currentOptionRow + '][product_option_value][' + optionValueRow + '][points_prefix]" class="form-select w-16">';
        html += '<option value="+">+</option>';
        html += '<option value="-">-</option>';
        html += '</select>';
        html += '<input type="text" name="product_option[' + currentOptionRow + '][product_option_value][' + optionValueRow + '][weight]" placeholder="Тегло" class="form-input w-24">';
        html += '<select name="product_option[' + currentOptionRow + '][product_option_value][' + optionValueRow + '][weight_prefix]" class="form-select w-16">';
        html += '<option value="+">+</option>';
        html += '<option value="-">-</option>';
        html += '</select>';
        html += '<button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-option-value">';
        html += '<i class="ri-delete-bin-line"></i>';
        html += '</button>';
        html += '</div>';

        $(this).before(html); // Add before the "Add Option Value" button
        $(this).data('option-value-row-start', optionValueRow + 1); // Update the data attribute
    });

    // Remove Option Value
    $(document).on('click', '.remove-option-value', function() {
        $(this).closest('.flex').remove();
    });

    // Add Special
    $('#button-special-add').on('click', function() {
        let specialRow = getNextRowIndex('#product-special');
        let html = '<div class="flex items-center space-x-2">';
        html += '<select name="product_special[' + specialRow + '][customer_group_id]" class="form-select flex-1">';
        {% for customer_group in customer_groups %}
            html += '<option value="{{ customer_group.customer_group_id }}">{{ customer_group.name }}</option>';
        {% endfor %}
        html += '</select>';
        html += '<input type="text" name="product_special[' + specialRow + '][priority]" placeholder="Приоритет" class="form-input w-24">';
        html += '<input type="text" name="product_special[' + specialRow + '][price]" placeholder="Цена" class="form-input w-24">';
        html += '<input type="date" name="product_special[' + specialRow + '][date_start]" placeholder="Начална дата" class="form-input w-32">';
        html += '<input type="date" name="product_special[' + specialRow + '][date_end]" placeholder="Крайна дата" class="form-input w-32">';
        html += '<button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-special">';
        html += '<i class="ri-delete-bin-line"></i>';
        html += '</button>';
        html += '</div>';

        $('#product-special').append(html);
    });

    // Remove Special
    $(document).on('click', '.remove-special', function() {
        $(this).closest('.flex').remove();
    });

    // Add Image
    $('#button-image-add').on('click', function() {
        let imageRow = getNextRowIndex('#images');
        let html = '<div class="flex items-center space-x-2">';
        html += '<a href="" id="thumb-image' + imageRow + '" data-toggle="image" class="img-thumbnail">';
        html += '<img src="{{ placeholder_image }}" alt="" title="" data-placeholder="{{ placeholder_image }}" />';
        html += '</a>';
        html += '<input type="hidden" name="product_image[' + imageRow + '][image]" value="" id="input-image' + imageRow + '" />';
        html += '<input type="text" name="product_image[' + imageRow + '][sort_order]" placeholder="Подредба" class="form-input w-24">';
        html += '<button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-image">';
        html += '<i class="ri-delete-bin-line"></i>';
        html += '</button>';
        html += '</div>';

        $('#images').append(html);
    });

    // Remove Image
    $(document).on('click', '.remove-image', function() {
        $(this).closest('.flex').remove();
    });

    // Add Reward
    $('#button-reward-add').on('click', function() {
        let rewardRow = getNextRowIndex('#product-reward');
        let html = '<div class="flex items-center space-x-2">';
        html += '<select name="product_reward[' + rewardRow + '][customer_group_id]" class="form-select flex-1">';
        {% for customer_group in customer_groups %}
            html += '<option value="{{ customer_group.customer_group_id }}">{{ customer_group.name }}</option>';
        {% endfor %}
        html += '</select>';
        html += '<input type="text" name="product_reward[' + rewardRow + '][points]" placeholder="Точки" class="form-input w-24">';
        html += '<button type="button" class="px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors remove-reward">';
        html += '<i class="ri-delete-bin-line"></i>';
        html += '</button>';
        html += '</div>';

        $('#product-reward').append(html);
    });

    // Remove Reward
    $(document).on('click', '.remove-reward', function() {
        $(this).closest('.flex').remove();
    });

    // Autocomplete for Filters, Related Products, Downloads (requires AJAX implementation)
    // Example for Filters
    $('#input-filter').autocomplete({
        'source': function(request, response) {
            $.ajax({
                url: 'index.php?route=catalog/filter/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
                dataType: 'json',
                success: function(json) {
                    response($.map(json, function(item) {
                        return {
                            label: item['name'],
                            value: item['filter_id']
                        }
                    }));
                }
            });
        },
        'select': function(item) {
            $('#input-filter').val('');
            $('#product-filter').append('<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">' + item['label'] + '<button type="button" class="ml-1 text-blue-800 hover:text-blue-900 remove-filter" data-filter-id="' + item['value'] + '">&times;</button><input type="hidden" name="product_filter[]" value="' + item['value'] + '"></span>');
        }
    });

    $('#product-filter').on('click', '.remove-filter', function() {
        $(this).parent().remove();
    });

    // Example for Related Products
    $('#input-related').autocomplete({
        'source': function(request, response) {
            $.ajax({
                url: 'index.php?route=catalog/product/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
                dataType: 'json',
                success: function(json) {
                    response($.map(json, function(item) {
                        return {
                            label: item['name'],
                            value: item['product_id']
                        }
                    }));
                }
            });
        },
        'select': function(item) {
            $('#input-related').val('');
            $('#product-related').append('<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">' + item['label'] + '<button type="button" class="ml-1 text-blue-800 hover:text-blue-900 remove-related" data-product-id="' + item['value'] + '">&times;</button><input type="hidden" name="product_related[]" value="' + item['value'] + '"></span>');
        }
    });

    $('#product-related').on('click', '.remove-related', function() {
        $(this).parent().remove();
    });

    // Example for Downloads
    $('#input-download').autocomplete({
        'source': function(request, response) {
            $.ajax({
                url: 'index.php?route=catalog/download/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
                dataType: 'json',
                success: function(json) {
                    response($.map(json, function(item) {
                        return {
                            label: item['name'],
                            value: item['download_id']
                        }
                    }));
                }
            });
        },
        'select': function(item) {
            $('#input-download').val('');
            $('#product-download').append('<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">' + item['label'] + '<button type="button" class="ml-1 text-blue-800 hover:text-blue-900 remove-download" data-download-id="' + item['value'] + '">&times;</button><input type="hidden" name="product_download[]" value="' + item['value'] + '"></span>');
        }
    });

    $('#product-download').on('click', '.remove-download', function() {
        $(this).parent().remove();
    });
</script>