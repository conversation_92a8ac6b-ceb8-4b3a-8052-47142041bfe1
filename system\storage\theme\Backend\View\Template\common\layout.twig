<!DOCTYPE html>
<html lang="{{ lang }}">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>{{ title }}</title>
<base href="{{ base }}" />
{% if description %}
<meta name="description" content="{{ description }}" />
{% endif %}
{% if keywords %}
<meta name="keywords" content="{{ keywords }}" />
{% endif %}
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#6e41b4',secondary:'#f8f9fa'},borderRadius:{'none':'0px','sm':'8px',DEFAULT:'12px','md':'16px','lg':'20px','xl':'24px','2xl':'28px','3xl':'32px','full':'9999px','button':'12px'}}}}</script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css">
<link href="https://fonts.googleapis.com/css2?family=Exo+2:wght@300;400;500;600;700&display=swap" rel="stylesheet">

{% if styles %}
{% for style in styles %}
<link type="text/css" href="{{ style.href }}" rel="{{ style.rel }}" media="{{ style.media }}" />
{% endfor %}
{% endif %}

{% if scripts %}
{% for script in scripts %}
<script type="text/javascript" src="{{ script }}"></script>
{% endfor %}
{% endif %}

</head>
<body class="flex h-screen overflow-hidden">
  {{ sidebar }}
  <div class="flex-1 flex flex-col overflow-hidden">
    {{ header }}
     {{ content }}
    {{ footer }}
  </div>

{% if scripts_footer %}
{% for script in scripts_footer %}
<script type="text/javascript" src="{{ script }}"></script>
{% endfor %}
{% endif %} 
</body>
</html>