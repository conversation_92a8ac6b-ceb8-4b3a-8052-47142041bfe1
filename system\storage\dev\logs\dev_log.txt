
{"Request":{"file":"\/home\/<USER>\/storage_theme25\/theme\/Backend\/Controller\/Sale\/Order\/Index.php","line":139},"Content":"Array\n(\n    [sort] => o.order_id\n    [order] => DESC\n    [start] => 0\n    [limit] => 20\n    [page] => 1\n)\n","_Date":["15.06.2025","08:33"]}
{"Request":{"file":"\/home\/<USER>\/storage_theme25\/theme\/Controller.php","line":402},"Content":">>>> renderTemplateWithDataAndOutput","_Date":["15.06.2025","08:33"]}
{"Request":{"file":"\/home\/<USER>\/storage_theme25\/theme\/Controller.php","line":403},"Content":"sale\/order","_Date":["15.06.2025","08:33"]}
{"Request":{"file":"\/home\/<USER>\/storage_theme25\/theme\/Controller.php","line":373},"Content":">>>> renderTemplate","_Date":["15.06.2025","08:33"]}
{"Request":{"file":"\/home\/<USER>\/storage_theme25\/theme\/Controller.php","line":374},"Content":"sale\/order","_Date":["15.06.2025","08:33"]}
{"Request":{"file":"\/home\/<USER>\/storage_theme25\/theme\/Controller.php","line":379},"Content":"Array\n(\n    [heading_title] => Поръчки\n    [user_token] => yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [add_new_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order\/add&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [delete_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order\/delete&order_id=ORDER_ID&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [edit_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order\/edit&order_id=ORDER_ID&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [info_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order\/info&order_id=ORDER_ID&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [invoice_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order\/invoice&order_id=ORDER_ID&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [shipping_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order\/shipping&order_id=ORDER_ID&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [sort_options] => Array\n        (\n            [0] => Array\n                (\n                    [value] => o.order_id-DESC\n                    [text] => Последно добавени\n                )\n\n            [1] => Array\n                (\n                    [value] => o.order_id-ASC\n                    [text] => Най-стари\n                )\n\n            [2] => Array\n                (\n                    [value] => o.total-ASC\n                    [text] => Сума (възх.)\n                )\n\n            [3] => Array\n                (\n                    [value] => o.total-DESC\n                    [text] => Сума (низх.)\n                )\n\n            [4] => Array\n                (\n                    [value] => customer-ASC\n                    [text] => Клиент (А-Я)\n                )\n\n            [5] => Array\n                (\n                    [value] => customer-DESC\n                    [text] => Клиент (Я-А)\n                )\n\n        )\n\n    [status_options] => Array\n        (\n            [0] => Array\n                (\n                    [value] => \n                    [text] => Всички статуси\n                )\n\n        )\n\n    [filter_data] => Array\n        (\n            [sort] => o.order_id\n            [order] => DESC\n            [start] => 0\n            [limit] => 20\n            [page] => 1\n        )\n\n    [sort_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order&sort=SORT_VALUE&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [limit_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order&limit=LIMIT_VALUE&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [filter_status_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order&filter_order_status_id=FILTER_VALUE&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [filter_customer_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order&filter_customer=FILTER_VALUE&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n    [filter_order_status_id] => \n    [filter_customer] => \n    [filter_date_added_start] => \n    [filter_date_added_end] => \n    [limit] => 20\n    [orders] => Array\n        (\n        )\n\n    [pagination_html] => \n    [back_url] => https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\n)\n","_Date":["15.06.2025","08:33"]}
{"Request":{"file":"\/home\/<USER>\/storage_theme25\/theme\/Controller.php","line":407},"Content":"<!DOCTYPE html>\n<html lang=\"bg\">\n<head>\n<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<title>Поръчки<\/title>\n<base href=\"https:\/\/theme25.rakla.bg\/admin\/\" \/><link rel=\"preconnect\" href=\"https:\/\/fonts.googleapis.com\">\n<link rel=\"preconnect\" href=\"https:\/\/fonts.gstatic.com\" crossorigin>\n<link href=\"https:\/\/fonts.googleapis.com\/css2?family=Pacifico&display=swap\" rel=\"stylesheet\">\n<script src=\"https:\/\/cdn.tailwindcss.com\/3.4.16\"><\/script>\n<script>tailwind.config={theme:{extend:{colors:{primary:'#6e41b4',secondary:'#f8f9fa'},borderRadius:{'none':'0px','sm':'8px',DEFAULT:'12px','md':'16px','lg':'20px','xl':'24px','2xl':'28px','3xl':'32px','full':'9999px','button':'12px'}}}}<\/script>\n<link rel=\"stylesheet\" href=\"https:\/\/cdn.jsdelivr.net\/npm\/remixicon@4.5.0\/fonts\/remixicon.css\">\n<link href=\"https:\/\/fonts.googleapis.com\/css2?family=Exo+2:wght@300;400;500;600;700&display=swap\" rel=\"stylesheet\"><link type=\"text\/css\" href=\"https:\/\/theme25.rakla.bg\/backend_css\/backend.css?v=1748668590\" rel=\"stylesheet\" media=\"screen\" \/><script type=\"text\/javascript\" src=\"https:\/\/theme25.rakla.bg\/backend_js\/backend.js?v=1746273157\"><\/script>\n<\/head>\n<body class=\"flex h-screen overflow-hidden\"><div id=\"sidebar\" class=\"w-64 h-full bg-white border-r border-gray-200 transition-all duration-300 ease-in-out\">\n    <div class=\"p-4 border-b border-gray-200\">\n        <div class=\"flex items-center\">\n            <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=common\/dashboard&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\">\n                <img src=\"https:\/\/www.rakla.bg\/image\/rakla-logo.svg\" alt=\"\" class=\"h-8 transition-all duration-300\" id=\"sidebar-logo\">\n            <\/a>\n            <button id=\"toggle-sidebar\" class=\"text-gray-500 hover:text-primary ml-auto\">\n                <div class=\"w-8 h-8 flex items-center justify-center\">\n                    <i class=\"ri-menu-fold-line ri-lg\"><\/i>\n                <\/div>\n            <\/button>\n        <\/div>\n    <\/div>\n    <div class=\"py-4\">\n        <ul>            <li>\n                <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=common\/dashboard&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" id=\"menu-dashboard\" class=\"sidebar-item flex items-center px-4 py-3 text-gray-700 hover:text-primary\">\n                    <div class=\"w-6 h-6 flex items-center justify-center mr-3\">\n                        <i class=\"ri-dashboard-line\"><\/i>\n                    <\/div>\n                    <span>Табло<\/span>\n                <\/a>\n            <\/li>            <li>\n                <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=catalog\/product&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" id=\"menu-products\" class=\"sidebar-item flex items-center px-4 py-3 text-gray-700 hover:text-primary\">\n                    <div class=\"w-6 h-6 flex items-center justify-center mr-3\">\n                        <i class=\"ri-shopping-bag-line\"><\/i>\n                    <\/div>\n                    <span>Продукти<\/span>\n                <\/a>\n            <\/li>            <li>\n                <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=catalog\/category&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" id=\"menu-categories\" class=\"sidebar-item flex items-center px-4 py-3 text-gray-700 hover:text-primary\">\n                    <div class=\"w-6 h-6 flex items-center justify-center mr-3\">\n                        <i class=\"ri-price-tag-3-line\"><\/i>\n                    <\/div>\n                    <span>Категории<\/span>\n                <\/a>\n            <\/li>            <li>\n                <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" id=\"menu-orders\" class=\"sidebar-item active flex items-center px-4 py-3 text-primary\">\n                    <div class=\"w-6 h-6 flex items-center justify-center mr-3\">\n                        <i class=\"ri-shopping-cart-line\"><\/i>\n                    <\/div>\n                    <span>Поръчки<\/span>\n                <\/a>\n            <\/li>            <li>\n                <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/request&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" id=\"menu-orders\" class=\"sidebar-item flex items-center px-4 py-3 text-gray-700 hover:text-primary\">\n                    <div class=\"w-6 h-6 flex items-center justify-center mr-3\">\n                        <i class=\"ri-questionnaire-line\"><\/i>\n                    <\/div>\n                    <span>Запитвания<\/span>\n                <\/a>\n            <\/li>            <li>\n                <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=setting\/setting&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" id=\"menu-settings\" class=\"sidebar-item flex items-center px-4 py-3 text-gray-700 hover:text-primary\">\n                    <div class=\"w-6 h-6 flex items-center justify-center mr-3\">\n                        <i class=\"ri-settings-line\"><\/i>\n                    <\/div>\n                    <span>Настройки<\/span>\n                <\/a>\n            <\/li>            <li>\n                <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=report\/report&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" id=\"menu-reports\" class=\"sidebar-item flex items-center px-4 py-3 text-gray-700 hover:text-primary\">\n                    <div class=\"w-6 h-6 flex items-center justify-center mr-3\">\n                        <i class=\"ri-bar-chart-line\"><\/i>\n                    <\/div>\n                    <span>Отчети<\/span>\n                <\/a>\n            <\/li>            <li>\n                <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=marketing\/marketing&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" id=\"menu-marketing\" class=\"sidebar-item flex items-center px-4 py-3 text-gray-700 hover:text-primary\">\n                    <div class=\"w-6 h-6 flex items-center justify-center mr-3\">\n                        <i class=\"ri-global-line\"><\/i>\n                    <\/div>\n                    <span>Маркетинг<\/span>\n                <\/a>\n            <\/li>            <li>\n                <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=system\/theme&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" id=\"menu-theme\" class=\"sidebar-item flex items-center px-4 py-3 text-gray-700 hover:text-primary\">\n                    <div class=\"w-6 h-6 flex items-center justify-center mr-3\">\n                        <i class=\"ri-palette-line\"><\/i>\n                    <\/div>\n                    <span>Тема<\/span>\n                <\/a>\n            <\/li>        <\/ul>\n    <\/div>\n<\/div>\n\n  <div class=\"flex-1 flex flex-col overflow-hidden\">\n<header class=\"bg-white border-b border-gray-200\">\n<div class=\"flex items-center justify-between p-4\">\n<div class=\"flex items-center\">\n<button id=\"mobile-menu\" class=\"mr-4 md:hidden text-gray-500 hover:text-primary\">\n<div class=\"w-8 h-8 flex items-center justify-center\">\n<i class=\"ri-menu-line ri-lg\"><\/i>\n<\/div>\n<\/button>\n<\/div>\n<div class=\"flex-1 mx-8\" style=\"max-width: 50rem;\">\n<div class=\"relative flex\">\n<div class=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n<div class=\"w-5 h-5 flex items-center justify-center text-gray-400\">\n<i class=\"ri-search-line\"><\/i>\n<\/div>\n<\/div>\n<input type=\"text\" class=\"search-input w-full pl-10 pr-4 py-2 border border-gray-300 rounded-l-button focus:outline-none focus:border-primary\" placeholder=\"Търсене на продукти, поръчки, клиенти...\">\n<button class=\"px-4 py-2 bg-primary text-white rounded-r-button hover:bg-primary\/90 transition-colors whitespace-nowrap\">\n<div class=\"w-5 h-5 flex items-center justify-center\">\n<i class=\"ri-search-line\"><\/i>\n<\/div>\n<\/button>\n<\/div>\n<\/div>\n<div class=\"flex items-center space-x-4\">\n<a href=\"https:\/\/theme25.rakla.bg\/\" target=\"_blank\" class=\"relative text-gray-600 hover:text-primary\">\n<div class=\"w-10 h-10 flex items-center justify-center\">\n<i class=\"ri-external-link-line ri-lg\"><\/i>\n<\/div>\n<\/a>\n<button class=\"relative text-gray-600 hover:text-primary\">\n<div class=\"w-10 h-10 flex items-center justify-center\">\n<i class=\"ri-notification-3-line ri-lg\"><\/i>\n<\/div><\/button>\n<div class=\"relative\">\n<button id=\"profile-menu-button\" class=\"flex items-center\">\n<div class=\"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600\">\n\n<i class=\"ri-user-line ri-lg\"><\/i>\n\n<\/div>\n<\/button>\n<\/div>\n<a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=common\/logout&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" class=\"flex items-center px-4 py-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-button transition-colors whitespace-nowrap\">\n<div class=\"w-5 h-5 flex items-center justify-center mr-2\">\n<i class=\"ri-logout-box-line\"><\/i>\n<\/div>\n<span>Изход<\/span>\n<\/a>\n<\/div>\n<\/div>\n<div class=\"flex justify-between border-b border-gray-200 pr-5\">\n<div class=\"flex\"><\/div>\n<button class=\"px-6 py-3 text-gray-600 hover:text-primary hover:bg-red-50 rounded-button transition-colors whitespace-nowrap flex items-center mb-1\">\n<div class=\"w-5 h-5 flex items-center justify-center mr-2\">\n<i class=\"ri-refresh-line\"><\/i>\n<\/div>\nКеш\n<\/button>\n<\/div>\n<\/header><!-- Order Header -->\n    <div class=\"bg-white border-b border-gray-200 px-6 py-4\">\n        <div class=\"flex flex-col md:flex-row md:items-center justify-between\">\n            <div>\n                <h1 class=\"text-2xl font-bold text-gray-800\">Поръчки<\/h1>\n                <p class=\"text-gray-500 mt-1\">Управление на всички поръчки в магазина<\/p>\n            <\/div>            <a href=\"https:\/\/theme25.rakla.bg\/admin\/index.php?route=sale\/order\/add&user_token=yDkzRA3rreFZybnJkNqmPdKlnG9x1Khe\" class=\"mt-4 md:mt-0 px-4 py-2 bg-primary text-white rounded-button hover:bg-primary\/90 transition-colors whitespace-nowrap flex items-center !rounded-button\">\n                <div class=\"w-5 h-5 flex items-center justify-center mr-2\">\n                    <i class=\"ri-add-line\"><\/i>\n                <\/div>\n                <span>Нова поръчка<\/span>\n            <\/a>        <\/div>\n    <\/div>\n\n    <!-- Filters -->\n    <div class=\"bg-white border-b border-gray-200 px-6 py-3\">\n        <div class=\"flex flex-wrap items-center gap-4\">\n            <button id=\"filter-btn\" class=\"px-4 py-2 bg-primary text-white rounded-button hover:bg-primary\/90 transition-colors whitespace-nowrap flex items-center !rounded-button\">\n                <div class=\"w-5 h-5 flex items-center justify-center mr-2\">\n                    <i class=\"ri-filter-3-line\"><\/i>\n                <\/div>\n                <span>Филтър<\/span>\n            <\/button>\n            <div class=\"w-full md:w-auto\">\n                <div class=\"relative\">\n                    <button id=\"status-dropdown-btn\" class=\"w-full md:w-48 flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm\">\n                        <span>Всички статуси\n                        <\/span>\n                        <div class=\"w-5 h-5 flex items-center justify-center\">\n                            <i class=\"ri-arrow-down-s-line\"><\/i>\n                        <\/div>\n                    <\/button>\n                    <div id=\"status-dropdown\" class=\"hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded shadow-lg\">\n                        <ul class=\"py-1\">                                <li class=\"px-4 py-2 hover:bg-gray-100 cursor-pointer\" data-value=\"\">Всички статуси<\/li>                        <\/ul>\n                    <\/div>\n                <\/div>\n            <\/div>\n            <div class=\"w-full md:w-auto\">\n                <div class=\"relative\">\n                    <button id=\"period-dropdown-btn\" class=\"w-full md:w-48 flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm\">\n                        <span>Период<\/span>\n                        <div class=\"w-5 h-5 flex items-center justify-center\">\n                            <i class=\"ri-arrow-down-s-line\"><\/i>\n                        <\/div>\n                    <\/button>\n                    <div id=\"period-dropdown\" class=\"hidden absolute z-10 w-64 mt-1 bg-white border border-gray-200 rounded shadow-lg p-4\">\n                        <div class=\"space-y-3\">\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-1\">От дата<\/label>\n                                <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm\">\n                            <\/div>\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-1\">До дата<\/label>\n                                <input type=\"date\" class=\"w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm\">\n                            <\/div>\n                            <div class=\"flex justify-end\">\n                                <button class=\"px-4 py-2 bg-primary text-white rounded-button hover:bg-primary\/90 text-sm whitespace-nowrap\">Приложи<\/button>\n                            <\/div>\n                        <\/div>\n                    <\/div>\n                <\/div>\n            <\/div>\n            <div class=\"w-full md:w-auto\">\n                <div class=\"relative\">\n                    <button id=\"sort-dropdown-btn\" class=\"w-full md:w-48 flex items-center justify-between px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:border-primary focus:outline-none focus:border-primary text-sm\">\n                        <span>Сортиране<\/span>\n                        <div class=\"w-5 h-5 flex items-center justify-center\">\n                            <i class=\"ri-arrow-down-s-line\"><\/i>\n                        <\/div>\n                    <\/button>\n                    <div id=\"sort-dropdown\" class=\"hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded shadow-lg\">\n                        <ul class=\"py-1\">                                <li class=\"px-4 py-2 hover:bg-gray-100 cursor-pointer\" data-value=\"o.order_id-DESC\">Последно добавени<\/li>                                <li class=\"px-4 py-2 hover:bg-gray-100 cursor-pointer\" data-value=\"o.order_id-ASC\">Най-стари<\/li>                                <li class=\"px-4 py-2 hover:bg-gray-100 cursor-pointer\" data-value=\"o.total-ASC\">Сума (възх.)<\/li>                                <li class=\"px-4 py-2 hover:bg-gray-100 cursor-pointer\" data-value=\"o.total-DESC\">Сума (низх.)<\/li>                                <li class=\"px-4 py-2 hover:bg-gray-100 cursor-pointer\" data-value=\"customer-ASC\">Клиент (А-Я)<\/li>                                <li class=\"px-4 py-2 hover:bg-gray-100 cursor-pointer\" data-value=\"customer-DESC\">Клиент (Я-А)<\/li>                        <\/ul>\n                    <\/div>\n                <\/div>\n            <\/div>\n\n            <!-- Filter Modal -->\n            <div id=\"filter-modal\" class=\"fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center\">\n                <div class=\"bg-white rounded-lg w-full max-w-md mx-4\">\n                    <div class=\"flex justify-between items-center p-6 border-b border-gray-200\">\n                        <h3 class=\"text-lg font-semibold text-gray-800\">Филтър<\/h3>\n                        <button id=\"close-filter\" class=\"text-gray-400 hover:text-gray-500\">\n                            <div class=\"w-6 h-6 flex items-center justify-center\">\n                                <i class=\"ri-close-line\"><\/i>\n                            <\/div>\n                        <\/button>\n                    <\/div>\n                    <div class=\"p-6\">\n                        <form id=\"filter-form\" class=\"space-y-4\">\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-1\">Статус<\/label>\n                                <select class=\"w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8\">\n                                    <option value=\"\">Всички<\/option>\n                                    <option value=\"new\">Нова<\/option>\n                                    <option value=\"processing\">В процес<\/option>\n                                    <option value=\"completed\">Изпълнена<\/option>\n                                    <option value=\"cancelled\">Отказана<\/option>\n                                <\/select>\n                            <\/div>\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-1\">Клиент<\/label>\n                                <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm\" placeholder=\"Име или имейл на клиент\">\n                            <\/div>\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-1\">Начин на плащане<\/label>\n                                <select class=\"w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8\">\n                                    <option value=\"\">Всички<\/option>\n                                    <option value=\"card\">Карта<\/option>\n                                    <option value=\"bank\">Банков превод<\/option>\n                                    <option value=\"cash\">Наложен платеж<\/option>\n                                <\/select>\n                            <\/div>\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-1\">Сума<\/label>\n                                <div class=\"flex space-x-2\">\n                                    <input type=\"number\" class=\"w-1\/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm\" placeholder=\"От\">\n                                    <input type=\"number\" class=\"w-1\/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm\" placeholder=\"До\">\n                                <\/div>\n                            <\/div>\n                            <div>\n                                <label class=\"block text-sm font-medium text-gray-700 mb-1\">Период<\/label>\n                                <div class=\"flex space-x-2\">\n                                    <input type=\"date\" class=\"w-1\/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm\">\n                                    <input type=\"date\" class=\"w-1\/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm\">\n                                <\/div>\n                            <\/div>\n                            <div class=\"flex justify-end space-x-2 mt-6\">\n                                <button type=\"button\" id=\"reset-filter\" class=\"px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap\">Изчисти<\/button>\n                                <button type=\"submit\" class=\"px-4 py-2 bg-primary text-white rounded-button hover:bg-primary\/90 text-sm whitespace-nowrap\">Приложи филтър<\/button>\n                            <\/div>\n                        <\/form>\n                    <\/div>\n                <\/div>\n            <\/div>\n        <\/div>\n    <\/div>\n\n    <!-- Main Content Area -->\n    <main class=\"flex-1 overflow-y-auto bg-gray-50 p-6\">\n        <!-- Orders Table -->\n        <div class=\"bg-white rounded shadow overflow-hidden\">\n            <div class=\"overflow-x-auto\">\n                <table class=\"min-w-full divide-y divide-gray-200\">\n                    <thead class=\"bg-gray-50\">\n                        <tr>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                <div class=\"flex items-center\">\n                                    <input type=\"checkbox\" class=\"mr-2\">\n                                    Номер\n                                <\/div>\n                            <\/th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Дата\n                            <\/th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Клиент\n                            <\/th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Начин на плащане\n                            <\/th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Статус\n                            <\/th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Сума\n                            <\/th>\n                            <th scope=\"col\" class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                Действия\n                            <\/th>\n                        <\/tr>\n                    <\/thead>\n                    <tbody class=\"bg-white divide-y divide-gray-200\">                            <tr>\n                                <td colspan=\"7\" class=\"px-6 py-4 text-center text-gray-500\">\n                                    Няма намерени поръчки\n                                <\/td>\n                            <\/tr>\n\n                    <\/tbody>\n                <\/table>\n            <\/div>\n            \n            <!-- Pagination -->        <\/div>\n    <\/main>\n\n<!-- Order Detail Modal -->\n<div id=\"order-detail-modal\" class=\"fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center\">\n    <div class=\"bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto\">\n        <div class=\"flex justify-between items-center p-6 border-b border-gray-200\">\n            <h3 class=\"text-lg font-semibold text-gray-800\">Детайли на поръчка #ORD-2025-1001<\/h3>\n            <button id=\"close-order-detail\" class=\"text-gray-400 hover:text-gray-500\">\n                <div class=\"w-6 h-6 flex items-center justify-center\">\n                    <i class=\"ri-close-line\"><\/i>\n                <\/div>\n            <\/button>\n        <\/div>\n        <div class=\"p-6\">\n            <div class=\"flex flex-col md:flex-row justify-between mb-6\">\n                <div>\n                    <h4 class=\"text-sm font-medium text-gray-500 mb-1\">Информация за поръчката<\/h4>\n                    <p class=\"text-sm\"><span class=\"font-medium\">Дата:<\/span> 23.04.2025 14:32<\/p>\n                    <p class=\"text-sm\"><span class=\"font-medium\">Статус:<\/span> <span class=\"text-blue-600\">Нова<\/span><\/p>\n                    <p class=\"text-sm\"><span class=\"font-medium\">Плащане:<\/span> Карта<\/p>\n                    <p class=\"text-sm\"><span class=\"font-medium\">Обща сума:<\/span> 1,599.00 лв.<\/p>\n                <\/div>\n                <div class=\"mt-4 md:mt-0\">\n                    <h4 class=\"text-sm font-medium text-gray-500 mb-1\">Информация за клиента<\/h4>\n                    <p class=\"text-sm\"><span class=\"font-medium\">Име:<\/span> Георги Иванов<\/p>\n                    <p class=\"text-sm\"><span class=\"font-medium\">Имейл:<\/span> <EMAIL><\/p>\n                    <p class=\"text-sm\"><span class=\"font-medium\">Телефон:<\/span> +359 888 123 456<\/p>\n                <\/div>\n                <div class=\"mt-4 md:mt-0\">\n                    <h4 class=\"text-sm font-medium text-gray-500 mb-1\">Адрес за доставка<\/h4>\n                    <p class=\"text-sm\">ул. Иван Вазов 25, ап. 7<\/p>\n                    <p class=\"text-sm\">София, 1000<\/p>\n                    <p class=\"text-sm\">България<\/p>\n                <\/div>\n            <\/div>\n            \n            <div class=\"border-t border-gray-200 pt-6 mb-6\">\n                <h4 class=\"font-medium text-gray-800 mb-4\">Продукти в поръчката<\/h4>\n                <div class=\"overflow-x-auto\">\n                    <table class=\"min-w-full divide-y divide-gray-200\">\n                        <thead class=\"bg-gray-50\">\n                            <tr>\n                                <th scope=\"col\" class=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                    Продукт\n                                <\/th>\n                                <th scope=\"col\" class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                    Количество\n                                <\/th>\n                                <th scope=\"col\" class=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                    Ед. цена\n                                <\/th>\n                                <th scope=\"col\" class=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                    Сума\n                                <\/th>\n                            <\/tr>\n                        <\/thead>\n                        <tbody class=\"bg-white divide-y divide-gray-200\">\n                            <tr>\n                                <td class=\"px-4 py-4\">\n                                    <div class=\"flex items-center\">\n                                        <div class=\"flex-shrink-0 h-10 w-10 bg-gray-100 rounded\">\n                                            <img class=\"h-10 w-10 rounded object-cover object-top\" src=\"https:\/\/readdy.ai\/api\/search-image?query=professional%2520photo%2520of%2520modern%2520smartphone%2520with%2520sleek%2520design%2520on%2520clean%2520white%2520background%252C%2520high%2520resolution%2520product%2520photography%252C%2520minimalist%2520style&width=100&height=100&seq=1&orientation=squarish\" alt=\"\">\n                                        <\/div>\n                                        <div class=\"ml-4\">\n                                            <div class=\"text-sm font-medium text-gray-900\">Смартфон Samsung Galaxy S22<\/div>\n                                            <div class=\"text-sm text-gray-500\">Код: PRD-1001<\/div>\n                                        <\/div>\n                                    <\/div>\n                                <\/td>\n                                <td class=\"px-4 py-4 text-center text-sm text-gray-900\">\n                                    1\n                                <\/td>\n                                <td class=\"px-4 py-4 text-right text-sm text-gray-900\">\n                                    1,599.00 лв.\n                                <\/td>\n                                <td class=\"px-4 py-4 text-right text-sm font-medium text-gray-900\">\n                                    1,599.00 лв.\n                                <\/td>\n                            <\/tr>\n                        <\/tbody>\n                        <tfoot>\n                            <tr class=\"bg-gray-50\">\n                                <td colspan=\"2\" class=\"px-4 py-3\"><\/td>\n                                <td class=\"px-4 py-3 text-right text-sm font-medium text-gray-700\">Междинна сума:<\/td>\n                                <td class=\"px-4 py-3 text-right text-sm font-medium text-gray-900\">1,599.00 лв.<\/td>\n                            <\/tr>\n                            <tr class=\"bg-gray-50\">\n                                <td colspan=\"2\" class=\"px-4 py-3\"><\/td>\n                                <td class=\"px-4 py-3 text-right text-sm font-medium text-gray-700\">Доставка:<\/td>\n                                <td class=\"px-4 py-3 text-right text-sm font-medium text-gray-900\">0.00 лв.<\/td>\n                            <\/tr>\n                            <tr class=\"bg-gray-50\">\n                                <td colspan=\"2\" class=\"px-4 py-3\"><\/td>\n                                <td class=\"px-4 py-3 text-right text-sm font-medium text-gray-700\">Обща сума:<\/td>\n                                <td class=\"px-4 py-3 text-right text-sm font-medium text-primary\">1,599.00 лв.<\/td>\n                            <\/tr>\n                        <\/tfoot>\n                    <\/table>\n                <\/div>\n            <\/div>\n            \n            <div class=\"border-t border-gray-200 pt-6 mb-6\">\n                <h4 class=\"font-medium text-gray-800 mb-4\">Промяна на статус<\/h4>\n                <div class=\"flex flex-wrap items-center gap-4\">\n                    <button class=\"px-4 py-2 bg-blue-100 text-blue-700 rounded-button hover:bg-blue-200 text-sm whitespace-nowrap\">Нова<\/button>\n                    <button class=\"px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 text-sm whitespace-nowrap\">В процес<\/button>\n                    <button class=\"px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 text-sm whitespace-nowrap\">Изпълнена<\/button>\n                    <button class=\"px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 text-sm whitespace-nowrap\">Отказана<\/button>\n                <\/div>\n            <\/div>\n            \n            <div class=\"border-t border-gray-200 pt-6\">\n                <h4 class=\"font-medium text-gray-800 mb-4\">Изпращане на известие<\/h4>\n                <div class=\"mb-4\">\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">Тема<\/label>\n                    <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm\" value=\"Информация за поръчка #ORD-2025-1001\">\n                <\/div>\n                <div class=\"mb-4\">\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">Съобщение<\/label>\n                    <textarea class=\"w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm h-32\" placeholder=\"Въведете текст на съобщението...\">Здравейте, Георги Иванов!\n\nБлагодарим ви за вашата поръчка #ORD-2025-1001. \n\nС уважение,\nЕкипът на Rakla<\/textarea>\n                <\/div>\n                <div class=\"flex justify-end\">\n                    <button class=\"px-4 py-2 bg-primary text-white rounded-button hover:bg-primary\/90 text-sm whitespace-nowrap\">Изпрати известие<\/button>\n                <\/div>\n            <\/div>\n        <\/div>\n    <\/div>\n<\/div>\n\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n    \/\/ Filter modal functionality\n    const filterBtn = document.getElementById('filter-btn');\n    const filterModal = document.getElementById('filter-modal');\n    const closeFilter = document.getElementById('close-filter');\n    const filterForm = document.getElementById('filter-form');\n    const resetFilter = document.getElementById('reset-filter');\n    \n    filterBtn.addEventListener('click', function() {\n        filterModal.classList.remove('hidden');\n        document.body.style.overflow = 'hidden';\n    });\n    \n    closeFilter.addEventListener('click', function() {\n        filterModal.classList.add('hidden');\n        document.body.style.overflow = 'auto';\n    });\n    \n    filterModal.addEventListener('click', function(e) {\n        if (e.target === filterModal) {\n            filterModal.classList.add('hidden');\n            document.body.style.overflow = 'auto';\n        }\n    });\n    \n    filterForm.addEventListener('submit', function(e) {\n        e.preventDefault();\n        filterModal.classList.add('hidden');\n        document.body.style.overflow = 'auto';\n    });\n    \n    resetFilter.addEventListener('click', function() {\n        filterForm.reset();\n    });\n    \n    \/\/ Toggle sidebar\n    const toggleSidebar = document.getElementById('toggle-sidebar');\n    const sidebar = document.getElementById('sidebar');\n    const mobileMenu = document.getElementById('mobile-menu');\n    \n    toggleSidebar.addEventListener('click', function() {\n        sidebar.classList.toggle('w-64');\n        sidebar.classList.toggle('w-20');\n        const sidebarItems = document.querySelectorAll('.sidebar-item span');\n        const sidebarLogo = document.getElementById('sidebar-logo');\n        sidebarItems.forEach(item => {\n            item.classList.toggle('hidden');\n        });\n        sidebarLogo.classList.toggle('hidden');\n        const icon = toggleSidebar.querySelector('i');\n        if (icon.classList.contains('ri-menu-fold-line')) {\n            icon.classList.remove('ri-menu-fold-line');\n            icon.classList.add('ri-menu-unfold-line');\n        } else {\n            icon.classList.remove('ri-menu-unfold-line');\n            icon.classList.add('ri-menu-fold-line');\n        }\n    });\n    \n    mobileMenu.addEventListener('click', function() {\n        sidebar.classList.toggle('-translate-x-full');\n    });\n    \n    \/\/ Status dropdown\n    const statusDropdownBtn = document.getElementById('status-dropdown-btn');\n    const statusDropdown = document.getElementById('status-dropdown');\n    \n    statusDropdownBtn.addEventListener('click', function() {\n        statusDropdown.classList.toggle('hidden');\n    });\n    \n    \/\/ Period dropdown\n    const periodDropdownBtn = document.getElementById('period-dropdown-btn');\n    const periodDropdown = document.getElementById('period-dropdown');\n    \n    periodDropdownBtn.addEventListener('click', function() {\n        periodDropdown.classList.toggle('hidden');\n    });\n    \n    \/\/ Sort dropdown\n    const sortDropdownBtn = document.getElementById('sort-dropdown-btn');\n    const sortDropdown = document.getElementById('sort-dropdown');\n    \n    sortDropdownBtn.addEventListener('click', function() {\n        sortDropdown.classList.toggle('hidden');\n    });\n    \n    \/\/ Per page dropdown\n    const perPageDropdownBtn = document.getElementById('per-page-dropdown-btn');\n    const perPageDropdown = document.getElementById('per-page-dropdown');\n    \n    perPageDropdownBtn.addEventListener('click', function() {\n        perPageDropdown.classList.toggle('hidden');\n    });\n    \n    \/\/ Close dropdowns when clicking outside\n    document.addEventListener('click', function(event) {\n        if (!statusDropdownBtn.contains(event.target) && !statusDropdown.contains(event.target)) {\n            statusDropdown.classList.add('hidden');\n        }\n        if (!periodDropdownBtn.contains(event.target) && !periodDropdown.contains(event.target)) {\n            periodDropdown.classList.add('hidden');\n        }\n        if (!sortDropdownBtn.contains(event.target) && !sortDropdown.contains(event.target)) {\n            sortDropdown.classList.add('hidden');\n        }\n        if (!perPageDropdownBtn.contains(event.target) && !perPageDropdown.contains(event.target)) {\n            perPageDropdown.classList.add('hidden');\n        }\n    });\n    \n    \/\/ Select status option\n    const statusItems = statusDropdown.querySelectorAll('li');\n    statusItems.forEach(item => {\n        item.addEventListener('click', function() {\n            statusDropdownBtn.querySelector('span').textContent = this.textContent;\n            statusDropdown.classList.add('hidden');\n        });\n    });\n    \n    \/\/ Select sort option\n    const sortItems = sortDropdown.querySelectorAll('li');\n    sortItems.forEach(item => {\n        item.addEventListener('click', function() {\n            sortDropdownBtn.querySelector('span').textContent = this.textContent;\n            sortDropdown.classList.add('hidden');\n        });\n    });\n    \n    \/\/ Select per page option\n    const perPageItems = perPageDropdown.querySelectorAll('li');\n    perPageItems.forEach(item => {\n        item.addEventListener('click', function() {\n            perPageDropdownBtn.querySelector('span').textContent = this.textContent;\n            perPageDropdown.classList.add('hidden');\n        });\n    });\n    \n    \/\/ Order detail modal\n    const viewOrderBtns = document.querySelectorAll('.ri-eye-line');\n    const orderDetailModal = document.getElementById('order-detail-modal');\n    const closeOrderDetail = document.getElementById('close-order-detail');\n    \n    viewOrderBtns.forEach(btn => {\n        btn.parentElement.parentElement.addEventListener('click', function() {\n            orderDetailModal.classList.remove('hidden');\n            document.body.style.overflow = 'hidden';\n        });\n    });\n    \n    closeOrderDetail.addEventListener('click', function() {\n        orderDetailModal.classList.add('hidden');\n        document.body.style.overflow = 'auto';\n    });\n    \n    orderDetailModal.addEventListener('click', function(e) {\n        if (e.target === orderDetailModal) {\n            orderDetailModal.classList.add('hidden');\n            document.body.style.overflow = 'auto';\n        }\n    });\n});\n<\/script>\n  <\/div> \n<\/body>\n<\/html>","_Date":["15.06.2025","08:33"]}