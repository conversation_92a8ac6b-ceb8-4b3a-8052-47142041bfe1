<?php

namespace Theme25\Backend\Controller\Marketing;

class Marketing extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'marketing/marketing');
    }

    public function index() {
        $this->setTitle('Маркетинг');

        // Инициализиране на данните
        $this->initAdminData();

        // Подготовка на данните с верижно извикване на методи
        // $this->prepareRequestListData()
        //      ->prepareFilterOptions()
        //      ->prepareRequestItems()
        //      ->preparePagination();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('marketing/marketing');
    }

   
}
